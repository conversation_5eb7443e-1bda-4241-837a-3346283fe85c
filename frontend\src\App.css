* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  border-radius: 8px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.header h1 {
  color: #2d5a27;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #2d5a27;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: #f0f8f0;
}

.logout-btn, .back-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.back-btn {
  background: #6c757d;
}

/* Auth Pages */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #2d5a27, #4a7c59);
}

.auth-card {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.logo h1 {
  text-align: center;
  color: #2d5a27;
  margin-bottom: 20px;
  font-size: 32px;
}

.auth-card h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input, .form-group select, .form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e1e1;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
  outline: none;
  border-color: #2d5a27;
}

.auth-button, .submit-btn, .add-product-btn, .checkout-btn {
  width: 100%;
  background: #2d5a27;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auth-button:hover, .submit-btn:hover, .add-product-btn:hover, .checkout-btn:hover {
  background: #1e3a1a;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

/* Search Section */
.search-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-input, .category-select {
  flex: 1;
  min-width: 200px;
  padding: 12px;
  border: 2px solid #e1e1e1;
  border-radius: 6px;
  font-size: 16px;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.product-image, .placeholder-image {
  height: 200px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-info h3 {
  margin-bottom: 8px;
  color: #333;
  font-size: 18px;
}

.product-price {
  font-size: 20px;
  font-weight: bold;
  color: #2d5a27;
  margin-bottom: 5px;
}

.product-category {
  color: #666;
  font-size: 14px;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.edit-btn, .delete-btn, .add-to-cart-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.edit-btn {
  background: #007bff;
  color: white;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.add-to-cart-btn {
  background: #28a745;
  color: white;
  padding: 12px 24px;
  font-size: 16px;
}

/* Loading and Empty States */
.loading, .no-products, .no-purchases, .empty-cart {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 18px;
}

/* Product Detail */
.product-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 20px;
}

.product-detail-image {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-detail-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.placeholder-image-large {
  height: 400px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 72px;
}

.product-detail-info {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-detail-info h1 {
  font-size: 28px;
  margin-bottom: 15px;
  color: #333;
}

.product-detail-price {
  font-size: 32px;
  font-weight: bold;
  color: #2d5a27;
  margin-bottom: 10px;
}

.product-detail-category {
  color: #666;
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.product-detail-description {
  line-height: 1.6;
  color: #555;
  margin-bottom: 30px;
  font-size: 16px;
}

/* Form Container */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

/* Dashboard */
.dashboard-container {
  max-width: 600px;
  margin: 0 auto;
}

.user-profile {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  text-align: center;
}

.avatar {
  width: 80px;
  height: 80px;
  background: #2d5a27;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto 20px;
}

.profile-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.save-btn, .cancel-btn, .edit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.save-btn {
  background: #28a745;
  color: white;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.edit-btn {
  background: #007bff;
  color: white;
}

/* Cart */
.cart-container {
  max-width: 800px;
  margin: 0 auto;
}

.cart-items {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.cart-item {
  display: flex;
  gap: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.cart-item-info {
  flex: 1;
}

.cart-item-info h3 {
  margin-bottom: 5px;
  color: #333;
}

.cart-item-category {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.cart-item-price {
  font-weight: bold;
  color: #2d5a27;
}

.cart-item-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity-controls button {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.cart-summary {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  text-align: center;
}

.total {
  margin-bottom: 20px;
}

.total h3 {
  font-size: 24px;
  color: #2d5a27;
}

/* Purchases */
.purchases-container {
  max-width: 800px;
  margin: 0 auto;
}

.purchase-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.purchase-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.purchase-header h3 {
  color: #333;
  margin-bottom: 5px;
}

.purchase-date {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.purchase-total {
  font-weight: bold;
  color: #2d5a27;
  font-size: 18px;
}

.purchase-item {
  display: flex;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.purchase-item:last-child {
  border-bottom: none;
}

.purchase-item-image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.purchase-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.purchase-item-info h4 {
  margin-bottom: 5px;
  color: #333;
  font-size: 16px;
}

.purchase-item-category {
  color: #666;
  font-size: 12px;
  margin-bottom: 5px;
}

.purchase-item-details {
  color: #2d5a27;
  font-weight: 500;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .search-section {
    flex-direction: column;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-detail {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cart-item {
    flex-direction: column;
    gap: 15px;
  }

  .cart-item-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .purchase-item {
    flex-direction: column;
    gap: 10px;
  }
}
