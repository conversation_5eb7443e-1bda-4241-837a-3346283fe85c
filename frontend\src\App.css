@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #2c3e50;
  line-height: 1.6;
  min-height: 100vh;
}

.App {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
}

.header h1 {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.header-actions {
  display: flex;
  gap: 20px;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #34495e;
  font-weight: 600;
  padding: 12px 20px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  transition: left 0.3s ease;
  z-index: -1;
}

.nav-link:hover::before {
  left: 0;
}

.nav-link:hover {
  color: white;
  transform: translateY(-2px);
}

.logout-btn, .back-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.logout-btn:hover, .back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.back-btn {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.back-btn:hover {
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

/* Auth Pages */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 50px;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.logo h1 {
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 30px;
  font-size: 36px;
  font-weight: 700;
}

.auth-card h2 {
  text-align: center;
  margin-bottom: 40px;
  color: #2d3748;
  font-weight: 600;
  font-size: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input, .form-group select, .form-group textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  font-family: inherit;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.auth-button, .submit-btn, .add-product-btn, .checkout-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  font-family: inherit;
}

.auth-button:hover, .submit-btn:hover, .add-product-btn:hover, .checkout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.auth-button:disabled, .submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

/* Search Section */
.search-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-input, .category-select {
  flex: 1;
  min-width: 200px;
  padding: 12px;
  border: 2px solid #e1e1e1;
  border-radius: 6px;
  font-size: 16px;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.product-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.product-image, .placeholder-image {
  height: 200px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-info h3 {
  margin-bottom: 8px;
  color: #333;
  font-size: 18px;
}

.product-price {
  font-size: 20px;
  font-weight: bold;
  color: #2d5a27;
  margin-bottom: 5px;
}

.product-category {
  color: #666;
  font-size: 14px;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.edit-btn, .delete-btn, .add-to-cart-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.edit-btn {
  background: #007bff;
  color: white;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.add-to-cart-btn {
  background: #28a745;
  color: white;
  padding: 12px 24px;
  font-size: 16px;
}

/* Loading and Empty States */
.loading, .no-products, .no-purchases, .empty-cart {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 18px;
}

/* Product Detail */
.product-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 20px;
}

.product-detail-image {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-detail-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.placeholder-image-large {
  height: 400px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 72px;
}

.product-detail-info {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-detail-info h1 {
  font-size: 28px;
  margin-bottom: 15px;
  color: #333;
}

.product-detail-price {
  font-size: 32px;
  font-weight: bold;
  color: #2d5a27;
  margin-bottom: 10px;
}

.product-detail-category {
  color: #666;
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.product-detail-description {
  line-height: 1.6;
  color: #555;
  margin-bottom: 30px;
  font-size: 16px;
}

/* Form Container */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

/* Dashboard */
.dashboard-container {
  max-width: 600px;
  margin: 0 auto;
}

.user-profile {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  text-align: center;
}

.avatar {
  width: 80px;
  height: 80px;
  background: #2d5a27;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto 20px;
}

.profile-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.save-btn, .cancel-btn, .edit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.save-btn {
  background: #28a745;
  color: white;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.edit-btn {
  background: #007bff;
  color: white;
}

/* Cart */
.cart-container {
  max-width: 800px;
  margin: 0 auto;
}

.cart-items {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.cart-item {
  display: flex;
  gap: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.cart-item-info {
  flex: 1;
}

.cart-item-info h3 {
  margin-bottom: 5px;
  color: #333;
}

.cart-item-category {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.cart-item-price {
  font-weight: bold;
  color: #2d5a27;
}

.cart-item-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity-controls button {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.cart-summary {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  text-align: center;
}

.total {
  margin-bottom: 20px;
}

.total h3 {
  font-size: 24px;
  color: #2d5a27;
}

/* Purchases */
.purchases-container {
  max-width: 800px;
  margin: 0 auto;
}

.purchase-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.purchase-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.purchase-header h3 {
  color: #333;
  margin-bottom: 5px;
}

.purchase-date {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.purchase-total {
  font-weight: bold;
  color: #2d5a27;
  font-size: 18px;
}

.purchase-item {
  display: flex;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.purchase-item:last-child {
  border-bottom: none;
}

.purchase-item-image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.purchase-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.purchase-item-info h4 {
  margin-bottom: 5px;
  color: #333;
  font-size: 16px;
}

.purchase-item-category {
  color: #666;
  font-size: 12px;
  margin-bottom: 5px;
}

.purchase-item-details {
  color: #2d5a27;
  font-weight: 500;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .search-section {
    flex-direction: column;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-detail {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cart-item {
    flex-direction: column;
    gap: 15px;
  }

  .cart-item-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .purchase-item {
    flex-direction: column;
    gap: 10px;
  }
}
