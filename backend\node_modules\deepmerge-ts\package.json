{"name": "deepmerge-ts", "version": "7.1.5", "description": "Deeply merge 2 or more objects respecting type information.", "keywords": ["merge", "deepmerge", "deep merge", "deep-merge", "inferred types", "inferred-types", "recursive merge", "recursive-merge", "ts", "ts merge", "ts-merge", "typescript", "typescript merge", "typescript-merge"], "homepage": "https://github.com/RebeccaStevens/deepmerge-ts#readme", "bugs": {"url": "https://github.com/RebeccaStevens/deepmerge-ts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/RebeccaStevens/deepmerge-ts.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "type": "module", "exports": {"types": {"import": "./dist/index.d.mts", "require": "./dist/index.d.cts"}, "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "main": "./dist/index.cjs", "types": "./dist/index.d.cts", "files": ["dist/", "package.json", "CHANGELOG.md", "LICENSE", "README.md"], "scripts": {"benchmark": "pnpm run build && cd benchmark && pnpm run benchmark; cd ..", "build": "rimraf dist && rollup -c rollup.config.ts --configPlugin @rollup/plugin-typescript", "cz": "git-cz", "lint": "eslint && pnpm run lint:md && pnpm run lint:spelling && pnpm run lint:knip && pnpm run lint:packages", "lint-fix": "eslint --fix && pnpm run lint:md-fix && pnpm run lint:packages-fix", "lint:js": "eslint \"**/*.?([cm])[jt]s?(x)\"", "lint:js-fix": "eslint \"**/*.?([cm])[jt]s?(x)\" --fix", "lint:knip": "pnpm run lint:knip:development && pnpm run lint:knip:production", "lint:knip:development": "knip --exclude exports,nsExports,types,nsTypes", "lint:knip:production": "knip --production --strict --exclude exports,nsExports,types,nsTypes", "lint:md": "markdownlint-cli2", "lint:md-fix": "markdownlint-cli2 --fix", "lint:md-full": "pnpm run lint:md && eslint \"**/*.md\"", "lint:md-full-fix": "pnpm run lint:md-fix && eslint \"**/*.md\" --fix", "lint:packages": "pnpm dedupe --check", "lint:packages-fix": "pnpm dedupe", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:yaml": "eslint \"**/*.y?(a)ml\"", "lint:yaml-fix": "eslint \"**/*.y?(a)ml\" --fix", "prepare": "husky", "release": "semantic-release", "test": "pnpm run test:js-run && pnpm run test:types", "test:js": "vitest --coverage", "test:js-run": "vitest run --coverage", "test:types": "pnpm run build && tsd -f 'tests/**/*.test-d.ts' -t 'dist/index.d.mts'", "typecheck": "tsc -p tsconfig.build.json --noEmit"}, "resolutions": {"dts-bundle-generator": "9.5.1"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@cspell/dict-cryptocurrencies": "5.0.3", "@eslint/compat": "1.2.2", "@rebeccastevens/eslint-config": "3.3.3", "@rollup/plugin-replace": "6.0.1", "@rollup/plugin-typescript": "12.1.1", "@sebbo2002/semantic-release-jsr": "2.0.1", "@semantic-release/changelog": "6.0.3", "@semantic-release/commit-analyzer": "13.0.0", "@semantic-release/git": "10.0.1", "@semantic-release/github": "11.0.0", "@semantic-release/npm": "12.0.1", "@semantic-release/release-notes-generator": "14.0.1", "@stylistic/eslint-plugin": "2.9.0", "@types/lodash": "4.17.13", "@types/node": "20.17.5", "@typescript-eslint/eslint-plugin": "8.8.1", "@typescript-eslint/parser": "8.8.1", "@vitest/coverage-v8": "2.1.2", "@vitest/eslint-plugin": "1.1.7", "commitizen": "4.3.1", "cspell": "8.15.0", "cz-conventional-changelog": "3.3.0", "eslint": "9.12.0", "eslint-config-prettier": "9.1.0", "eslint-flat-config-utils": "0.4.0", "eslint-import-resolver-typescript": "3.6.3", "eslint-merge-processors": "0.1.0", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-format": "0.1.2", "eslint-plugin-functional": "7.1.0", "eslint-plugin-import-x": "4.4.0", "eslint-plugin-jsdoc": "50.4.3", "eslint-plugin-jsonc": "2.16.0", "eslint-plugin-markdown": "5.1.0", "eslint-plugin-n": "17.12.0", "eslint-plugin-no-only-tests": "3.3.0", "eslint-plugin-optimize-regex": "1.2.1", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-promise": "7.1.0", "eslint-plugin-regexp": "2.6.0", "eslint-plugin-sonarjs": "2.0.4", "eslint-plugin-unicorn": "56.0.0", "eslint-plugin-yml": "1.15.0", "husky": "9.1.6", "jsonc-eslint-parser": "2.4.0", "knip": "5.36.1", "lint-staged": "15.2.10", "lodash": "4.17.21", "markdownlint-cli2": "0.14.0", "prettier": "3.3.3", "prettier-plugin-packagejson": "2.5.3", "rimraf": "6.0.1", "rollup": "4.24.3", "rollup-plugin-deassert": "1.3.0", "rollup-plugin-dts-bundle-generator": "1.4.0", "semantic-release": "24.1.2", "tsc-files": "1.1.4", "tsd": "0.31.2", "typescript": "5.6.3", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.2", "yaml-eslint-parser": "1.2.3"}, "packageManager": "pnpm@9.12.3", "engines": {"node": ">=16.0.0"}}