{"version": 3, "file": "mergeDecision.js", "names": ["_Function", "require", "_Predicate", "OpCodes", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MergeDecisionSymbolKey", "MergeDecisionTypeId", "exports", "Symbol", "for", "proto", "_R", "_", "_E0", "_Z0", "_E", "_Z", "Done", "effect", "op", "create", "_tag", "OP_DONE", "Await", "OP_AWAIT", "AwaitConst", "isMergeDecision", "u", "hasProperty", "match", "dual", "self", "onAwait", "onDone"], "sources": ["../../../../src/internal/channel/mergeDecision.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAEA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,uBAAA,CAAAH,OAAA;AAA6D,SAAAG,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,CAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE7D;AACA,MAAMkB,sBAAsB,GAAG,6BAA6B;AAE5D;AACO,MAAMC,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,gBAAsCE,MAAM,CAACC,GAAG,CAC9EJ,sBAAsB,CACc;AAEtC;AACA,MAAMK,KAAK,GAAG;EACZ,CAACJ,mBAAmB,GAAG;IACrBK,EAAE,EAAGC,CAAQ,IAAKA,CAAC;IACnBC,GAAG,EAAGD,CAAU,IAAKA,CAAC;IACtBE,GAAG,EAAGF,CAAU,IAAKA,CAAC;IACtBG,EAAE,EAAGH,CAAQ,IAAKA,CAAC;IACnBI,EAAE,EAAGJ,CAAQ,IAAKA;;CAErB;AA6BD;AACO,MAAMK,IAAI,GACfC,MAA8B,IAC4B;EAC1D,MAAMC,EAAE,GAAGjB,MAAM,CAACkB,MAAM,CAACV,KAAK,CAAC;EAC/BS,EAAE,CAACE,IAAI,GAAGrC,OAAO,CAACsC,OAAO;EACzBH,EAAE,CAACD,MAAM,GAAGA,MAAM;EAClB,OAAOC,EAAE;AACX,CAAC;AAED;AAAAZ,OAAA,CAAAU,IAAA,GAAAA,IAAA;AACO,MAAMM,KAAK,GAChB7B,CAAsD,IACN;EAChD,MAAMyB,EAAE,GAAGjB,MAAM,CAACkB,MAAM,CAACV,KAAK,CAAC;EAC/BS,EAAE,CAACE,IAAI,GAAGrC,OAAO,CAACwC,QAAQ;EAC1BL,EAAE,CAACzB,CAAC,GAAGA,CAAC;EACR,OAAOyB,EAAE;AACX,CAAC;AAED;AAAAZ,OAAA,CAAAgB,KAAA,GAAAA,KAAA;AACO,MAAME,UAAU,GACrBP,MAA8B,IAC6BK,KAAK,CAAC,MAAML,MAAM,CAAC;AAEhF;AAAAX,OAAA,CAAAkB,UAAA,GAAAA,UAAA;AACO,MAAMC,eAAe,GAC1BC,CAAU,IACwE,IAAAC,sBAAW,EAACD,CAAC,EAAErB,mBAAmB,CAAC;AAEvH;AAAAC,OAAA,CAAAmB,eAAA,GAAAA,eAAA;AACO,MAAMG,KAAK,GAAAtB,OAAA,CAAAsB,KAAA,gBAAG,IAAAC,cAAI,EAcvB,CAAC,EAAE,CACHC,IAAkD,EAClD;EAAEC,OAAO;EAAEC;AAAM,CAGhB,KACK;EACN,MAAMd,EAAE,GAAGY,IAAiB;EAC5B,QAAQZ,EAAE,CAACE,IAAI;IACb,KAAKrC,OAAO,CAACsC,OAAO;MAClB,OAAOW,MAAM,CAACd,EAAE,CAACD,MAAM,CAAC;IAC1B,KAAKlC,OAAO,CAACwC,QAAQ;MACnB,OAAOQ,OAAO,CAACb,EAAE,CAACzB,CAAC,CAAC;EACxB;AACF,CAAC,CAAC", "ignoreList": []}