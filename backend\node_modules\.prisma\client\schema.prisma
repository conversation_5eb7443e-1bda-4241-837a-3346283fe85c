datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        Int        @id @default(autoincrement())
  email     String     @unique
  username  String?
  password  String
  products  Product[]
  purchases Purchase[]
  createdAt DateTime   @default(now())
}

model Product {
  id          Int        @id @default(autoincrement())
  title       String
  description String
  category    String
  price       Float
  image       String?
  ownerId     Int
  owner       User       @relation(fields: [ownerId], references: [id])
  purchases   Purchase[] // <-- This line creates the other side of the many-to-many relation
  createdAt   DateTime   @default(now())
}

model Purchase {
  id        Int       @id @default(autoincrement())
  userId    Int
  user      User      @relation(fields: [userId], references: [id])
  products  Product[] // <-- This sets up the join table automatically
  createdAt DateTime  @default(now())
}
