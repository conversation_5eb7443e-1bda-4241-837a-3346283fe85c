{"version": 3, "file": "subexecutor.js", "names": ["Effect", "_interopRequireWildcard", "require", "Exit", "_Function", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "OP_PULL_FROM_CHILD", "exports", "OP_PULL_FROM_UPSTREAM", "OP_DRAIN_CHILD_EXECUTORS", "OP_EMIT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "child<PERSON><PERSON><PERSON>or", "parentSubexecutor", "onEmit", "_tag", "constructor", "close", "exit", "fin1", "fin2", "undefined", "zipWith", "exit1", "exit2", "pipe", "zipRight", "enqueue<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_child", "PullFromUpstream", "upstreamExecutor", "create<PERSON><PERSON>d", "lastDone", "activeChildExecutors", "combineChildResults", "combineWithChildResult", "onPull", "fins", "map", "child", "result", "reduce", "acc", "next", "DrainChildExecutors", "upstreamDone", "Emit", "value"], "sources": ["../../../../src/internal/channel/subexecutor.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAAwC,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAcxC;AACO,MAAMkB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,eAAwB;AAK1D;AACO,MAAME,qBAAqB,GAAAD,OAAA,CAAAC,qBAAA,GAAG,kBAA2B;AAKhE;AACO,MAAMC,wBAAwB,GAAAF,OAAA,CAAAE,wBAAA,GAAG,qBAA8B;AAKtE;AACO,MAAMC,OAAO,GAAAH,OAAA,CAAAG,OAAA,GAAG,MAAe;AAKtC;;;;;;AAMM,MAAOC,aAAa;EAIbC,aAAA;EACAC,iBAAA;EACAC,MAAA;EALFC,IAAI,GAAuBT,kBAAkB;EAEtDU,YACWJ,aAAgC,EAChCC,iBAAiC,EACjCC,MAAuE;IAFvE,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;EAEjB;EAEAG,KAAKA,CAACC,IAAiC;IACrC,MAAMC,IAAI,GAAG,IAAI,CAACP,aAAa,CAACK,KAAK,CAACC,IAAI,CAAC;IAC3C,MAAME,IAAI,GAAG,IAAI,CAACP,iBAAiB,CAACI,KAAK,CAACC,IAAI,CAAC;IAC/C,IAAIC,IAAI,KAAKE,SAAS,IAAID,IAAI,KAAKC,SAAS,EAAE;MAC5C,OAAOvC,MAAM,CAACwC,OAAO,CACnBxC,MAAM,CAACoC,IAAI,CAACC,IAAI,CAAC,EACjBrC,MAAM,CAACoC,IAAI,CAACE,IAAI,CAAC,EACjB,CAACG,KAAK,EAAEC,KAAK,KAAK,IAAAC,cAAI,EAACF,KAAK,EAAEtC,IAAI,CAACyC,QAAQ,CAACF,KAAK,CAAC,CAAC,CACpD;IACH,CAAC,MAAM,IAAIL,IAAI,KAAKE,SAAS,EAAE;MAC7B,OAAOF,IAAI;IACb,CAAC,MAAM,IAAIC,IAAI,KAAKC,SAAS,EAAE;MAC7B,OAAOD,IAAI;IACb,CAAC,MAAM;MACL,OAAOC,SAAS;IAClB;EACF;EAEAM,oBAAoBA,CAACC,MAAwB;IAC3C,OAAO,IAAI;EACb;;AAGF;;;;;;AAAArB,OAAA,CAAAI,aAAA,GAAAA,aAAA;AAMM,MAAOkB,gBAAgB;EAIhBC,gBAAA;EACAC,WAAA;EACAC,QAAA;EACAC,oBAAA;EACAC,mBAAA;EACAC,sBAAA;EACAC,MAAA;EAGAtB,MAAA;EAZFC,IAAI,GAA0BP,qBAAqB;EAE5DQ,YACWc,gBAAmC,EACnCC,WAAiD,EACjDC,QAAiB,EACjBC,oBAAiE,EACjEC,mBAAwD,EACxDC,sBAA2D,EAC3DC,MAE8C,EAC9CtB,MAAuE;IATvE,KAAAgB,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,MAAM,GAANA,MAAM;IAGN,KAAAtB,MAAM,GAANA,MAAM;EAEjB;EAEAG,KAAKA,CAACC,IAAiC;IACrC,MAAMC,IAAI,GAAG,IAAI,CAACW,gBAAgB,CAACb,KAAK,CAACC,IAAI,CAAC;IAC9C,MAAMmB,IAAI,GAAG,CACX,GAAG,IAAI,CAACJ,oBAAoB,CAACK,GAAG,CAAEC,KAAK,IACrCA,KAAK,KAAKlB,SAAS,GACjBkB,KAAK,CAAC3B,aAAa,CAACK,KAAK,CAACC,IAAI,CAAC,GAC/BG,SAAS,CACZ,EACDF,IAAI,CACL;IACD,MAAMqB,MAAM,GAAGH,IAAI,CAACI,MAAM,CACxB,CAACC,GAAqE,EAAEC,IAAI,KAAI;MAC9E,IAAID,GAAG,KAAKrB,SAAS,IAAIsB,IAAI,KAAKtB,SAAS,EAAE;QAC3C,OAAOvC,MAAM,CAACwC,OAAO,CACnBoB,GAAG,EACH5D,MAAM,CAACoC,IAAI,CAACyB,IAAI,CAAC,EACjB,CAACpB,KAAK,EAAEC,KAAK,KAAKvC,IAAI,CAACyC,QAAQ,CAACH,KAAK,EAAEC,KAAK,CAAC,CAC9C;MACH,CAAC,MAAM,IAAIkB,GAAG,KAAKrB,SAAS,EAAE;QAC5B,OAAOqB,GAAG;MACZ,CAAC,MAAM,IAAIC,IAAI,KAAKtB,SAAS,EAAE;QAC7B,OAAOvC,MAAM,CAACoC,IAAI,CAACyB,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,OAAOtB,SAAS;MAClB;IACF,CAAC,EACDA,SAAS,CACV;IACD,OAAOmB,MAAM,KAAKnB,SAAS,GAAGmB,MAAM,GAAGA,MAAM;EAC/C;EAEAb,oBAAoBA,CAACY,KAAuB;IAC1C,OAAO,IAAIV,gBAAgB,CACzB,IAAI,CAACC,gBAAgB,EACrB,IAAI,CAACC,WAAW,EAChB,IAAI,CAACC,QAAQ,EACb,CAAC,GAAG,IAAI,CAACC,oBAAoB,EAAEM,KAAK,CAAC,EACrC,IAAI,CAACL,mBAAmB,EACxB,IAAI,CAACC,sBAAsB,EAC3B,IAAI,CAACC,MAAM,EACX,IAAI,CAACtB,MAAM,CACZ;EACH;;AAGF;;;;;;AAAAP,OAAA,CAAAsB,gBAAA,GAAAA,gBAAA;AAMM,MAAOe,mBAAmB;EAInBd,gBAAA;EACAE,QAAA;EACAC,oBAAA;EACAY,YAAA;EACAX,mBAAA;EACAC,sBAAA;EACAC,MAAA;EATFrB,IAAI,GAA6BN,wBAAwB;EAElEO,YACWc,gBAAmC,EACnCE,QAAiB,EACjBC,oBAAiE,EACjEY,YAAyC,EACzCX,mBAAwD,EACxDC,sBAA2D,EAC3DC,MAE8C;IAR9C,KAAAN,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAE,QAAQ,GAARA,QAAQ;IACR,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAY,YAAY,GAAZA,YAAY;IACZ,KAAAX,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,MAAM,GAANA,MAAM;EAIjB;EAEAnB,KAAKA,CAACC,IAAiC;IACrC,MAAMC,IAAI,GAAG,IAAI,CAACW,gBAAgB,CAACb,KAAK,CAACC,IAAI,CAAC;IAC9C,MAAMmB,IAAI,GAAG,CACX,GAAG,IAAI,CAACJ,oBAAoB,CAACK,GAAG,CAAEC,KAAK,IAAMA,KAAK,KAAKlB,SAAS,GAC9DkB,KAAK,CAAC3B,aAAa,CAACK,KAAK,CAACC,IAAI,CAAC,GAC/BG,SAAU,CACX,EACDF,IAAI,CACL;IACD,MAAMqB,MAAM,GAAGH,IAAI,CAACI,MAAM,CACxB,CAACC,GAAqE,EAAEC,IAAI,KAAI;MAC9E,IAAID,GAAG,KAAKrB,SAAS,IAAIsB,IAAI,KAAKtB,SAAS,EAAE;QAC3C,OAAOvC,MAAM,CAACwC,OAAO,CACnBoB,GAAG,EACH5D,MAAM,CAACoC,IAAI,CAACyB,IAAI,CAAC,EACjB,CAACpB,KAAK,EAAEC,KAAK,KAAKvC,IAAI,CAACyC,QAAQ,CAACH,KAAK,EAAEC,KAAK,CAAC,CAC9C;MACH,CAAC,MAAM,IAAIkB,GAAG,KAAKrB,SAAS,EAAE;QAC5B,OAAOqB,GAAG;MACZ,CAAC,MAAM,IAAIC,IAAI,KAAKtB,SAAS,EAAE;QAC7B,OAAOvC,MAAM,CAACoC,IAAI,CAACyB,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,OAAOtB,SAAS;MAClB;IACF,CAAC,EACDA,SAAS,CACV;IACD,OAAOmB,MAAM,KAAKnB,SAAS,GAAGmB,MAAM,GAAGA,MAAM;EAC/C;EAEAb,oBAAoBA,CAACY,KAAuB;IAC1C,OAAO,IAAIK,mBAAmB,CAC5B,IAAI,CAACd,gBAAgB,EACrB,IAAI,CAACE,QAAQ,EACb,CAAC,GAAG,IAAI,CAACC,oBAAoB,EAAEM,KAAK,CAAC,EACrC,IAAI,CAACM,YAAY,EACjB,IAAI,CAACX,mBAAmB,EACxB,IAAI,CAACC,sBAAsB,EAC3B,IAAI,CAACC,MAAM,CACZ;EACH;;AAGF;AAAA7B,OAAA,CAAAqC,mBAAA,GAAAA,mBAAA;AACM,MAAOE,IAAI;EAGMC,KAAA;EAAyBJ,IAAA;EAFrC5B,IAAI,GAAYL,OAAO;EAEhCM,YAAqB+B,KAAc,EAAWJ,IAAoB;IAA7C,KAAAI,KAAK,GAALA,KAAK;IAAoB,KAAAJ,IAAI,GAAJA,IAAI;EAClD;EAEA1B,KAAKA,CAACC,IAAiC;IACrC,MAAMsB,MAAM,GAAG,IAAI,CAACG,IAAI,CAAC1B,KAAK,CAACC,IAAI,CAAC;IACpC,OAAOsB,MAAM,KAAKnB,SAAS,GAAGmB,MAAM,GAAGA,MAAM;EAC/C;EAEAb,oBAAoBA,CAACC,MAAwB;IAC3C,OAAO,IAAI;EACb", "ignoreList": []}