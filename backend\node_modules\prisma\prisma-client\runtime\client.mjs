import * as __banner_node_module from "node:module";
import * as __banner_node_path from "node:path";
import * as process from "node:process";
import * as __banner_node_url from "node:url";
const __filename = __banner_node_url.fileURLToPath(import.meta.url);
globalThis['__dirname'] = __banner_node_path.dirname(__filename);
const require = __banner_node_module.createRequire(import.meta.url);
var hu=Object.create;var li=Object.defineProperty;var wu=Object.getOwnPropertyDescriptor;var Eu=Object.getOwnPropertyNames;var xu=Object.getPrototypeOf,bu=Object.prototype.hasOwnProperty;var Ht=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var Jo=(e,t)=>()=>(e&&(t=e(e=0)),t);var H=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Gt=(e,t)=>{for(var r in t)li(e,r,{get:t[r],enumerable:!0})},Pu=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Eu(t))!bu.call(e,i)&&i!==r&&li(e,i,{get:()=>t[i],enumerable:!(n=wu(t,i))||n.enumerable});return e};var fe=(e,t,r)=>(r=e!=null?hu(xu(e)):{},Pu(t||!e||!e.__esModule?li(r,"default",{value:e,enumerable:!0}):r,e));var cs=H((Dy,Qu)=>{Qu.exports={name:"@prisma/internals",version:"6.15.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",empathic:"2.0.0",esbuild:"0.25.5","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.15.0-5.85179d7826409ee107a6ba334b5e305ae3fba9fb","@prisma/schema-engine-wasm":"6.15.0-5.85179d7826409ee107a6ba334b5e305ae3fba9fb","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var di=H((jy,Wu)=>{Wu.exports={name:"@prisma/engines-version",version:"6.15.0-5.85179d7826409ee107a6ba334b5e305ae3fba9fb",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"85179d7826409ee107a6ba334b5e305ae3fba9fb"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var us=H(zr=>{"use strict";Object.defineProperty(zr,"__esModule",{value:!0});zr.enginesVersion=void 0;zr.enginesVersion=di().prisma.enginesVersion});var ds=H((Qy,ps)=>{"use strict";ps.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var ys=H((Jy,gs)=>{"use strict";gs.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Es=H((Ky,ws)=>{"use strict";ws.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var yi=H((zy,xs)=>{"use strict";var Xu=Es();xs.exports=e=>typeof e=="string"?e.replace(Xu(),""):e});var bs=H((eh,ep)=>{ep.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var Cs=H((th,ke)=>{"use strict";var wi=Ht("node:fs"),Ei=Ht("node:path"),tp=Ht("node:os"),rp=Ht("node:crypto"),np=bs(),vs=np.version,ip=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function op(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=ip.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function sp(e){let t=As(e),r=j.configDotenv({path:t});if(!r.parsed){let s=new Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw s.code="MISSING_DATA",s}let n=Ts(e).split(","),i=n.length,o;for(let s=0;s<i;s++)try{let a=n[s].trim(),l=lp(r,a);o=j.decrypt(l.ciphertext,l.key);break}catch(a){if(s+1>=i)throw a}return j.parse(o)}function ap(e){console.log(`[dotenv@${vs}][WARN] ${e}`)}function Xt(e){console.log(`[dotenv@${vs}][DEBUG] ${e}`)}function Ts(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function lp(e,t){let r;try{r=new URL(t)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let n=r.password;if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let i=r.searchParams.get("environment");if(!i){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:s,key:n}}function As(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)wi.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=Ei.resolve(process.cwd(),".env.vault");return wi.existsSync(t)?t:null}function Ps(e){return e[0]==="~"?Ei.join(tp.homedir(),e.slice(1)):e}function cp(e){!!(e&&e.debug)&&Xt("Loading env from encrypted .env.vault");let r=j._parseVault(e),n=process.env;return e&&e.processEnv!=null&&(n=e.processEnv),j.populate(n,r,e),{parsed:r}}function up(e){let t=Ei.resolve(process.cwd(),".env"),r="utf8",n=!!(e&&e.debug);e&&e.encoding?r=e.encoding:n&&Xt("No encoding is specified. UTF-8 is used by default");let i=[t];if(e&&e.path)if(!Array.isArray(e.path))i=[Ps(e.path)];else{i=[];for(let l of e.path)i.push(Ps(l))}let o,s={};for(let l of i)try{let c=j.parse(wi.readFileSync(l,{encoding:r}));j.populate(s,c,e)}catch(c){n&&Xt(`Failed to load ${l} ${c.message}`),o=c}let a=process.env;return e&&e.processEnv!=null&&(a=e.processEnv),j.populate(a,s,e),o?{parsed:s,error:o}:{parsed:s}}function pp(e){if(Ts(e).length===0)return j.configDotenv(e);let t=As(e);return t?j._configVault(e):(ap(`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`),j.configDotenv(e))}function dp(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let s=rp.createDecipheriv("aes-256-gcm",r,i);return s.setAuthTag(o),`${s.update(n)}${s.final()}`}catch(s){let a=s instanceof RangeError,l=s.message==="Invalid key length",c=s.message==="Unsupported state or unable to authenticate data";if(a||l){let u=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw u.code="INVALID_DOTENV_KEY",u}else if(c){let u=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw u.code="DECRYPTION_FAILED",u}else throw s}}function mp(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if(typeof t!="object"){let o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(let o of Object.keys(t))Object.prototype.hasOwnProperty.call(e,o)?(i===!0&&(e[o]=t[o]),n&&Xt(i===!0?`"${o}" is already defined and WAS overwritten`:`"${o}" is already defined and was NOT overwritten`)):e[o]=t[o]}var j={configDotenv:up,_configVault:cp,_parseVault:sp,config:pp,decrypt:dp,parse:op,populate:mp};ke.exports.configDotenv=j.configDotenv;ke.exports._configVault=j._configVault;ke.exports._parseVault=j._parseVault;ke.exports.config=j.config;ke.exports.decrypt=j.decrypt;ke.exports.parse=j.parse;ke.exports.populate=j.populate;ke.exports=j});var ks=H((ch,en)=>{"use strict";en.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};en.exports.default=en.exports});var Oi=H((Fw,Zs)=>{"use strict";Zs.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,c,u,p,d,m,y,g,I,A,C,w,k=[];for(l=0;l<i;l++)k.push(l+1),k.push(t.charCodeAt(s+l));for(var me=k.length-1;a<o-3;)for(I=r.charCodeAt(s+(c=a)),A=r.charCodeAt(s+(u=a+1)),C=r.charCodeAt(s+(p=a+2)),w=r.charCodeAt(s+(d=a+3)),m=a+=4,l=0;l<me;l+=2)y=k[l],g=k[l+1],c=e(y,c,u,I,g),u=e(c,u,p,A,g),p=e(u,p,d,C,g),m=e(p,d,m,w,g),k[l]=m,d=p,p=u,u=c,c=y;for(;a<o;)for(I=r.charCodeAt(s+(c=a)),m=++a,l=0;l<me;l+=2)y=k[l],k[l]=m=e(y,c,m,I,k[l+1]),c=y;return m}}()});var na=Jo(()=>{"use strict"});var ia=Jo(()=>{"use strict"});var io=H(et=>{"use strict";Object.defineProperty(et,"__esModule",{value:!0});et.anumber=no;et.abytes=xl;et.ahash=Ef;et.aexists=xf;et.aoutput=bf;function no(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function wf(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function xl(e,...t){if(!wf(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Ef(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");no(e.outputLen),no(e.blockLen)}function xf(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function bf(e,t){xl(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var jl=H(v=>{"use strict";Object.defineProperty(v,"__esModule",{value:!0});v.add5L=v.add5H=v.add4H=v.add4L=v.add3H=v.add3L=v.rotlBL=v.rotlBH=v.rotlSL=v.rotlSH=v.rotr32L=v.rotr32H=v.rotrBL=v.rotrBH=v.rotrSL=v.rotrSH=v.shrSL=v.shrSH=v.toBig=void 0;v.fromBig=so;v.split=bl;v.add=Ml;var Ln=BigInt(2**32-1),oo=BigInt(32);function so(e,t=!1){return t?{h:Number(e&Ln),l:Number(e>>oo&Ln)}:{h:Number(e>>oo&Ln)|0,l:Number(e&Ln)|0}}function bl(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=so(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var Pl=(e,t)=>BigInt(e>>>0)<<oo|BigInt(t>>>0);v.toBig=Pl;var vl=(e,t,r)=>e>>>r;v.shrSH=vl;var Tl=(e,t,r)=>e<<32-r|t>>>r;v.shrSL=Tl;var Al=(e,t,r)=>e>>>r|t<<32-r;v.rotrSH=Al;var Cl=(e,t,r)=>e<<32-r|t>>>r;v.rotrSL=Cl;var Rl=(e,t,r)=>e<<64-r|t>>>r-32;v.rotrBH=Rl;var Sl=(e,t,r)=>e>>>r-32|t<<64-r;v.rotrBL=Sl;var Il=(e,t)=>t;v.rotr32H=Il;var kl=(e,t)=>e;v.rotr32L=kl;var Ol=(e,t,r)=>e<<r|t>>>32-r;v.rotlSH=Ol;var Dl=(e,t,r)=>t<<r|e>>>32-r;v.rotlSL=Dl;var Nl=(e,t,r)=>t<<r-32|e>>>64-r;v.rotlBH=Nl;var _l=(e,t,r)=>e<<r-32|t>>>64-r;v.rotlBL=_l;function Ml(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var Fl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);v.add3L=Fl;var Ll=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;v.add3H=Ll;var $l=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);v.add4L=$l;var Vl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;v.add4H=Vl;var ql=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);v.add5L=ql;var Ul=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;v.add5H=Ul;var Pf={fromBig:so,split:bl,toBig:Pl,shrSH:vl,shrSL:Tl,rotrSH:Al,rotrSL:Cl,rotrBH:Rl,rotrBL:Sl,rotr32H:Il,rotr32L:kl,rotlSH:Ol,rotlSL:Dl,rotlBH:Nl,rotlBL:_l,add:Ml,add3L:Fl,add3H:Ll,add4L:$l,add4H:Vl,add5H:Ul,add5L:ql};v.default=Pf});var Bl=H($n=>{"use strict";Object.defineProperty($n,"__esModule",{value:!0});$n.crypto=void 0;var Qe=Ht("node:crypto");$n.crypto=Qe&&typeof Qe=="object"&&"webcrypto"in Qe?Qe.webcrypto:Qe&&typeof Qe=="object"&&"randomBytes"in Qe?Qe:void 0});var Gl=H(S=>{"use strict";Object.defineProperty(S,"__esModule",{value:!0});S.Hash=S.nextTick=S.byteSwapIfBE=S.isLE=void 0;S.isBytes=vf;S.u8=Tf;S.u32=Af;S.createView=Cf;S.rotr=Rf;S.rotl=Sf;S.byteSwap=co;S.byteSwap32=If;S.bytesToHex=Of;S.hexToBytes=Df;S.asyncLoop=_f;S.utf8ToBytes=Hl;S.toBytes=Vn;S.concatBytes=Mf;S.checkOpts=Ff;S.wrapConstructor=Lf;S.wrapConstructorWithOpts=$f;S.wrapXOFConstructorWithOpts=Vf;S.randomBytes=qf;var It=Bl(),lo=io();function vf(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Tf(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Af(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Cf(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Rf(e,t){return e<<32-t|e>>>t}function Sf(e,t){return e<<t|e>>>32-t>>>0}S.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function co(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}S.byteSwapIfBE=S.isLE?e=>e:e=>co(e);function If(e){for(let t=0;t<e.length;t++)e[t]=co(e[t])}var kf=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Of(e){(0,lo.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=kf[e[r]];return t}var _e={_0:48,_9:57,A:65,F:70,a:97,f:102};function Ql(e){if(e>=_e._0&&e<=_e._9)return e-_e._0;if(e>=_e.A&&e<=_e.F)return e-(_e.A-10);if(e>=_e.a&&e<=_e.f)return e-(_e.a-10)}function Df(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=Ql(e.charCodeAt(o)),a=Ql(e.charCodeAt(o+1));if(s===void 0||a===void 0){let l=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+o)}n[i]=s*16+a}return n}var Nf=async()=>{};S.nextTick=Nf;async function _f(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,S.nextTick)(),n+=o)}}function Hl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Vn(e){return typeof e=="string"&&(e=Hl(e)),(0,lo.abytes)(e),e}function Mf(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,lo.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var ao=class{clone(){return this._cloneInto()}};S.Hash=ao;function Ff(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function Lf(e){let t=n=>e().update(Vn(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function $f(e){let t=(n,i)=>e(i).update(Vn(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Vf(e){let t=(n,i)=>e(i).update(Vn(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function qf(e=32){if(It.crypto&&typeof It.crypto.getRandomValues=="function")return It.crypto.getRandomValues(new Uint8Array(e));if(It.crypto&&typeof It.crypto.randomBytes=="function")return It.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var ec=H(V=>{"use strict";Object.defineProperty(V,"__esModule",{value:!0});V.shake256=V.shake128=V.keccak_512=V.keccak_384=V.keccak_256=V.keccak_224=V.sha3_512=V.sha3_384=V.sha3_256=V.sha3_224=V.Keccak=void 0;V.keccakP=Zl;var kt=io(),Pr=jl(),Me=Gl(),Kl=[],zl=[],Yl=[],Uf=BigInt(0),br=BigInt(1),jf=BigInt(2),Bf=BigInt(7),Qf=BigInt(256),Hf=BigInt(113);for(let e=0,t=br,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],Kl.push(2*(5*n+r)),zl.push((e+1)*(e+2)/2%64);let i=Uf;for(let o=0;o<7;o++)t=(t<<br^(t>>Bf)*Hf)%Qf,t&jf&&(i^=br<<(br<<BigInt(o))-br);Yl.push(i)}var[Gf,Jf]=(0,Pr.split)(Yl,!0),Jl=(e,t,r)=>r>32?(0,Pr.rotlBH)(e,t,r):(0,Pr.rotlSH)(e,t,r),Wl=(e,t,r)=>r>32?(0,Pr.rotlBL)(e,t,r):(0,Pr.rotlSL)(e,t,r);function Zl(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,l=(s+2)%10,c=r[l],u=r[l+1],p=Jl(c,u,1)^r[a],d=Wl(c,u,1)^r[a+1];for(let m=0;m<50;m+=10)e[s+m]^=p,e[s+m+1]^=d}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=zl[s],l=Jl(i,o,a),c=Wl(i,o,a),u=Kl[s];i=e[u],o=e[u+1],e[u]=l,e[u+1]=c}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=Gf[n],e[1]^=Jf[n]}r.fill(0)}var vr=class e extends Me.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,kt.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Me.u32)(this.state)}keccak(){Me.isLE||(0,Me.byteSwap32)(this.state32),Zl(this.state32,this.rounds),Me.isLE||(0,Me.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,kt.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Me.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,kt.aexists)(this,!1),(0,kt.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,kt.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,kt.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};V.Keccak=vr;var He=(e,t,r)=>(0,Me.wrapConstructor)(()=>new vr(t,e,r));V.sha3_224=He(6,144,224/8);V.sha3_256=He(6,136,256/8);V.sha3_384=He(6,104,384/8);V.sha3_512=He(6,72,512/8);V.keccak_224=He(1,144,224/8);V.keccak_256=He(1,136,256/8);V.keccak_384=He(1,104,384/8);V.keccak_512=He(1,72,512/8);var Xl=(e,t,r)=>(0,Me.wrapXOFConstructorWithOpts)((n={})=>new vr(t,e,n.dkLen===void 0?r:n.dkLen,!0));V.shake128=Xl(31,168,128/8);V.shake256=Xl(31,136,256/8)});var lc=H((QP,Ge)=>{"use strict";var{sha3_512:Wf}=ec(),rc=24,Tr=32,uo=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function nc(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var ic=(e="")=>nc(Wf(e)).toString(36).slice(1),tc=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),Kf=e=>tc[Math.floor(e()*tc.length)],oc=({globalObj:e=typeof global<"u"?global:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+uo(Tr,t):uo(Tr,t);return ic(n).substring(0,Tr)},sc=e=>()=>e++,zf=476782367,ac=({random:e=Math.random,counter:t=sc(Math.floor(e()*zf)),length:r=rc,fingerprint:n=oc({random:e})}={})=>function(){let o=Kf(e),s=Date.now().toString(36),a=t().toString(36),l=uo(r,e),c=`${s+l+a+n}`;return`${o+ic(c).substring(1,r)}`},Yf=ac(),Zf=(e,{minLength:t=2,maxLength:r=Tr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};Ge.exports.getConstants=()=>({defaultLength:rc,bigLength:Tr});Ge.exports.init=ac;Ge.exports.createId=Yf;Ge.exports.bufToBigInt=nc;Ge.exports.createCounter=sc;Ge.exports.createFingerprint=oc;Ge.exports.isCuid=Zf});var cc=H((HP,Ar)=>{"use strict";var{createId:Xf,init:eg,getConstants:tg,isCuid:rg}=lc();Ar.exports.createId=Xf;Ar.exports.init=eg;Ar.exports.getConstants=tg;Ar.exports.isCuid=rg});var zo={};Gt(zo,{defineExtension:()=>Wo,getExtensionContext:()=>Ko});function Wo(e){return typeof e=="function"?e:t=>t.$extends(e)}function Ko(e){return e}var Zo={};Gt(Zo,{validator:()=>Yo});function Yo(...e){return t=>t}var Wr={};Gt(Wr,{$:()=>ns,bgBlack:()=>Du,bgBlue:()=>Fu,bgCyan:()=>$u,bgGreen:()=>_u,bgMagenta:()=>Lu,bgRed:()=>Nu,bgWhite:()=>Vu,bgYellow:()=>Mu,black:()=>Su,blue:()=>ze,bold:()=>X,cyan:()=>Se,dim:()=>We,gray:()=>Wt,green:()=>Jt,grey:()=>Ou,hidden:()=>Cu,inverse:()=>Au,italic:()=>Tu,magenta:()=>Iu,red:()=>Re,reset:()=>vu,strikethrough:()=>Ru,underline:()=>ie,white:()=>ku,yellow:()=>Ke});var ci,Xo,es,ts,rs=!0;typeof process<"u"&&({FORCE_COLOR:ci,NODE_DISABLE_COLORS:Xo,NO_COLOR:es,TERM:ts}=process.env||{},rs=process.stdout&&process.stdout.isTTY);var ns={enabled:!Xo&&es==null&&ts!=="dumb"&&(ci!=null&&ci!=="0"||rs)};function F(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!ns.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var vu=F(0,0),X=F(1,22),We=F(2,22),Tu=F(3,23),ie=F(4,24),Au=F(7,27),Cu=F(8,28),Ru=F(9,29),Su=F(30,39),Re=F(31,39),Jt=F(32,39),Ke=F(33,39),ze=F(34,39),Iu=F(35,39),Se=F(36,39),ku=F(37,39),Wt=F(90,39),Ou=F(90,39),Du=F(40,49),Nu=F(41,49),_u=F(42,49),Mu=F(43,49),Fu=F(44,49),Lu=F(45,49),$u=F(46,49),Vu=F(47,49);var qu=100,is=["green","yellow","blue","magenta","cyan","red"],Kt=[],os=Date.now(),Uu=0,ui=typeof process<"u"?process.env:{};globalThis.DEBUG??=ui.DEBUG??"";globalThis.DEBUG_COLORS??=ui.DEBUG_COLORS?ui.DEBUG_COLORS==="true":!0;var zt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function ju(e){let t={color:is[Uu++%is.length],enabled:zt.enabled(e),namespace:e,log:zt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Kt.push([o,...n]),Kt.length>qu&&Kt.shift(),zt.enabled(o)||i){let l=n.map(u=>typeof u=="string"?u:Bu(u)),c=`+${Date.now()-os}ms`;os=Date.now(),globalThis.DEBUG_COLORS?a(Wr[s](X(o)),...l,Wr[s](c)):a(o,...l,c)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var q=new Proxy(ju,{get:(e,t)=>zt[t],set:(e,t,r)=>zt[t]=r});function Bu(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function ss(e=7500){let t=Kt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function as(){Kt.length=0}var ls=q;var Hu=cs(),pi=Hu.version;function ut(e){let t=Gu();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":Ju(e))}function Gu(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function Ju(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}function Kr(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}var x={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};var ms=fe(ds(),1);function mi(e){let t=(0,ms.default)(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}var fs="prisma+postgres",Yr=`${fs}:`;function Zr(e){return e?.toString().startsWith(`${Yr}//`)??!1}function fi(e){if(!Zr(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")||t.includes("[::1]")}var Zt={};Gt(Zt,{error:()=>Yu,info:()=>zu,log:()=>Ku,query:()=>Zu,should:()=>hs,tags:()=>Yt,warn:()=>gi});var Yt={error:Re("prisma:error"),warn:Ke("prisma:warn"),info:Se("prisma:info"),query:ze("prisma:query")},hs={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function Ku(...e){console.log(...e)}function gi(e,...t){hs.warn()&&console.warn(`${Yt.warn} ${e}`,...t)}function zu(e,...t){console.info(`${Yt.info} ${e}`,...t)}function Yu(e,...t){console.error(`${Yt.error} ${e}`,...t)}function Zu(e,...t){console.log(`${Yt.query} ${e}`,...t)}function Ie(e,t){throw new Error(t)}import Xr from"node:path";function hi(e){return Xr.sep===Xr.posix.sep?e:e.split(Xr.sep).join(Xr.posix.sep)}var Pi=fe(Cs());import xi from"node:fs";import er from"node:path";function Rs(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],c,u;if(l==="\\")u=a[0],c=u.replace("\\$","$");else{let p=a[2];u=a[0].substring(l.length),c=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",c=r(c)}return o.replace(u,c)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var bi=ls("prisma:tryLoadEnv");function tr({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=Ss(e);r.conflictCheck!=="none"&&fp(n,t,r.conflictCheck);let i=null;return Is(n?.path,t)||(i=Ss(t)),!n&&!i&&bi("No Environment variables loaded"),i?.dotenvResult.error?console.error(Re(X("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function fp(e,t,r){let n=e?.dotenvResult.parsed,i=!Is(e?.path,t);if(n&&t&&i&&xi.existsSync(t)){let o=Pi.default.parse(xi.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=er.relative(process.cwd(),e.path),l=er.relative(process.cwd(),t);if(r==="error"){let c=`There is a conflict between env var${s.length>1?"s":""} in ${ie(a)} and ${ie(l)}
Conflicting env vars:
${s.map(u=>`  ${X(u)}`).join(`
`)}

We suggest to move the contents of ${ie(l)} to ${ie(a)} to consolidate your env vars.
`;throw new Error(c)}else if(r==="warn"){let c=`Conflict for env var${s.length>1?"s":""} ${s.map(u=>X(u)).join(", ")} in ${ie(a)} and ${ie(l)}
Env vars from ${ie(l)} overwrite the ones from ${ie(a)}
      `;console.warn(`${Ke("warn(prisma)")} ${c}`)}}}}function Ss(e){if(gp(e)){bi(`Environment variables loaded from ${e}`);let t=Pi.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:Rs(t),message:We(`Environment variables loaded from ${er.relative(process.cwd(),e)}`),path:e}}else bi(`Environment variables not found at ${e}`);return null}function Is(e,t){return e&&t&&er.resolve(e)===er.resolve(t)}function gp(e){return!!(e&&xi.existsSync(e))}function vi(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function tn(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function Ti(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function P(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Os=new Set,rn=(e,t,...r)=>{Os.has(e)||(Os.add(e),gi(t,...r))};var _=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};P(_,"PrismaClientInitializationError");var B=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};P(B,"PrismaClientKnownRequestError");var oe=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};P(oe,"PrismaClientRustPanicError");var Y=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};P(Y,"PrismaClientUnknownRequestError");var ee=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};P(ee,"PrismaClientValidationError");var Ee=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function Le(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function Ns(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}function rr(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function yp(e){return{models:Ai(e.models),enums:Ai(e.enums),types:Ai(e.types)}}function Ai(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function pt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function nn(e){return e.toString()!=="Invalid Date"}var dt=9e15,Ue=1e9,Ci="0123456789abcdef",an="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",ln="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",Ri={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-dt,maxE:dt,crypto:!1},Ls,Oe,b=!0,un="[DecimalError] ",qe=un+"Invalid argument: ",$s=un+"Precision limit exceeded",Vs=un+"crypto unavailable",qs="[object Decimal]",Z=Math.floor,Q=Math.pow,hp=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,wp=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,Ep=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,Us=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ge=1e7,E=7,xp=9007199254740991,bp=an.length-1,Si=ln.length-1,f={toStringTag:qs};f.absoluteValue=f.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),h(e)};f.ceil=function(){return h(new this.constructor(this),this.e+1,2)};f.clampedTo=f.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(qe+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};f.comparedTo=f.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,c=e.s;if(!s||!a)return!l||!c?NaN:l!==c?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-c:0;if(l!==c)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};f.cosine=f.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=Pp(n,Gs(n,r)),n.precision=e,n.rounding=t,h(Oe==2||Oe==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};f.cubeRoot=f.cbrt=function(){var e,t,r,n,i,o,s,a,l,c,u=this,p=u.constructor;if(!u.isFinite()||u.isZero())return new p(u);for(b=!1,o=u.s*Q(u.s*u,1/3),!o||Math.abs(o)==1/0?(r=J(u.d),e=u.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=Q(r,1/3),e=Z((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=u.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),c=l.plus(u),n=M(c.plus(u).times(a),c.plus(l),s+2,1),J(a.d).slice(0,s)===(r=J(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(h(a,e+1,0),a.times(a).times(a).eq(u))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(h(n,e+1,1),t=!n.times(n).times(n).eq(u));break}return b=!0,h(n,e,p.rounding,t)};f.decimalPlaces=f.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-Z(this.e/E))*E,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};f.dividedBy=f.div=function(e){return M(this,new this.constructor(e))};f.dividedToIntegerBy=f.divToInt=function(e){var t=this,r=t.constructor;return h(M(t,new r(e),0,1,1),r.precision,r.rounding)};f.equals=f.eq=function(e){return this.cmp(e)===0};f.floor=function(){return h(new this.constructor(this),this.e+1,3)};f.greaterThan=f.gt=function(e){return this.cmp(e)>0};f.greaterThanOrEqualTo=f.gte=function(e){var t=this.cmp(e);return t==1||t===0};f.hyperbolicCosine=f.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/dn(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=mt(s,1,o.times(t),new s(1),!0);for(var l,c=e,u=new s(8);c--;)l=o.times(o),o=a.minus(l.times(u.minus(l.times(u))));return h(o,s.precision=r,s.rounding=n,!0)};f.hyperbolicSine=f.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=mt(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/dn(5,e)),i=mt(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),c=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(c))))}return o.precision=t,o.rounding=r,h(i,t,r,!0)};f.hyperbolicTangent=f.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,M(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};f.inverseCosine=f.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return r!==-1?r===0?e.isNeg()?xe(t,n,i):new t(0):new t(NaN):e.isZero()?xe(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))};f.inverseHyperbolicCosine=f.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,b=!1,r=r.times(r).minus(1).sqrt().plus(r),b=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};f.inverseHyperbolicSine=f.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,b=!1,r=r.times(r).plus(1).sqrt().plus(r),b=!0,n.precision=e,n.rounding=t,r.ln())};f.inverseHyperbolicTangent=f.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?h(new o(i),e,t,!0):(o.precision=r=n-i.e,i=M(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};f.inverseSine=f.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=xe(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};f.inverseTangent=f.atan=function(){var e,t,r,n,i,o,s,a,l,c=this,u=c.constructor,p=u.precision,d=u.rounding;if(c.isFinite()){if(c.isZero())return new u(c);if(c.abs().eq(1)&&p+4<=Si)return s=xe(u,p+4,d).times(.25),s.s=c.s,s}else{if(!c.s)return new u(NaN);if(p+4<=Si)return s=xe(u,p+4,d).times(.5),s.s=c.s,s}for(u.precision=a=p+10,u.rounding=1,r=Math.min(28,a/E+2|0),e=r;e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(b=!1,t=Math.ceil(a/E),n=1,l=c.times(c),s=new u(c),i=c;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),b=!0,h(s,u.precision=p,u.rounding=d,!0)};f.isFinite=function(){return!!this.d};f.isInteger=f.isInt=function(){return!!this.d&&Z(this.e/E)>this.d.length-2};f.isNaN=function(){return!this.s};f.isNegative=f.isNeg=function(){return this.s<0};f.isPositive=f.isPos=function(){return this.s>0};f.isZero=function(){return!!this.d&&this.d[0]===0};f.lessThan=f.lt=function(e){return this.cmp(e)<0};f.lessThanOrEqualTo=f.lte=function(e){return this.cmp(e)<1};f.logarithm=f.log=function(e){var t,r,n,i,o,s,a,l,c=this,u=c.constructor,p=u.precision,d=u.rounding,m=5;if(e==null)e=new u(10),t=!0;else{if(e=new u(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new u(NaN);t=e.eq(10)}if(r=c.d,c.s<0||!r||!r[0]||c.eq(1))return new u(r&&!r[0]?-1/0:c.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(b=!1,a=p+m,s=Ve(c,a),n=t?cn(u,a+10):Ve(e,a),l=M(s,n,a,1),nr(l.d,i=p,d))do if(a+=10,s=Ve(c,a),n=t?cn(u,a+10):Ve(e,a),l=M(s,n,a,1),!o){+J(l.d).slice(i+1,i+15)+1==1e14&&(l=h(l,p+1,0));break}while(nr(l.d,i+=10,d));return b=!0,h(l,p,d)};f.minus=f.sub=function(e){var t,r,n,i,o,s,a,l,c,u,p,d,m=this,y=m.constructor;if(e=new y(e),!m.d||!e.d)return!m.s||!e.s?e=new y(NaN):m.d?e.s=-e.s:e=new y(e.d||m.s!==e.s?m:NaN),e;if(m.s!=e.s)return e.s=-e.s,m.plus(e);if(c=m.d,d=e.d,a=y.precision,l=y.rounding,!c[0]||!d[0]){if(d[0])e.s=-e.s;else if(c[0])e=new y(m);else return new y(l===3?-0:0);return b?h(e,a,l):e}if(r=Z(e.e/E),u=Z(m.e/E),c=c.slice(),o=u-r,o){for(p=o<0,p?(t=c,o=-o,s=d.length):(t=d,r=u,s=c.length),n=Math.max(Math.ceil(a/E),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=c.length,s=d.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(c[n]!=d[n]){p=c[n]<d[n];break}o=0}for(p&&(t=c,c=d,d=t,e.s=-e.s),s=c.length,n=d.length-s;n>0;--n)c[s++]=0;for(n=d.length;n>o;){if(c[--n]<d[n]){for(i=n;i&&c[--i]===0;)c[i]=ge-1;--c[i],c[n]+=ge}c[n]-=d[n]}for(;c[--s]===0;)c.pop();for(;c[0]===0;c.shift())--r;return c[0]?(e.d=c,e.e=pn(c,r),b?h(e,a,l):e):new y(l===3?-0:0)};f.modulo=f.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?h(new n(r),n.precision,n.rounding):(b=!1,n.modulo==9?(t=M(r,e.abs(),0,3,1),t.s*=e.s):t=M(r,e,0,n.modulo,1),t=t.times(e),b=!0,r.minus(t))};f.naturalExponential=f.exp=function(){return Ii(this)};f.naturalLogarithm=f.ln=function(){return Ve(this)};f.negated=f.neg=function(){var e=new this.constructor(this);return e.s=-e.s,h(e)};f.plus=f.add=function(e){var t,r,n,i,o,s,a,l,c,u,p=this,d=p.constructor;if(e=new d(e),!p.d||!e.d)return!p.s||!e.s?e=new d(NaN):p.d||(e=new d(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(c=p.d,u=e.d,a=d.precision,l=d.rounding,!c[0]||!u[0])return u[0]||(e=new d(p)),b?h(e,a,l):e;if(o=Z(p.e/E),n=Z(e.e/E),c=c.slice(),i=o-n,i){for(i<0?(r=c,i=-i,s=u.length):(r=u,n=o,s=c.length),o=Math.ceil(a/E),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=c.length,i=u.length,s-i<0&&(i=s,r=u,u=c,c=r),t=0;i;)t=(c[--i]=c[i]+u[i]+t)/ge|0,c[i]%=ge;for(t&&(c.unshift(t),++n),s=c.length;c[--s]==0;)c.pop();return e.d=c,e.e=pn(c,n),b?h(e,a,l):e};f.precision=f.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(qe+e);return r.d?(t=js(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};f.round=function(){var e=this,t=e.constructor;return h(new t(e),e.e+1,t.rounding)};f.sine=f.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=Tp(n,Gs(n,r)),n.precision=e,n.rounding=t,h(Oe>2?r.neg():r,e,t,!0)):new n(NaN)};f.squareRoot=f.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,c=s.s,u=s.constructor;if(c!==1||!a||!a[0])return new u(!c||c<0&&(!a||a[0])?NaN:a?s:1/0);for(b=!1,c=Math.sqrt(+s),c==0||c==1/0?(t=J(a),(t.length+l)%2==0&&(t+="0"),c=Math.sqrt(t),l=Z((l+1)/2)-(l<0||l%2),c==1/0?t="5e"+l:(t=c.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new u(t)):n=new u(c.toString()),r=(l=u.precision)+3;;)if(o=n,n=o.plus(M(s,o,r+2,1)).times(.5),J(o.d).slice(0,r)===(t=J(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(h(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(h(n,l+1,1),e=!n.times(n).eq(s));break}return b=!0,h(n,l,u.rounding,e)};f.tangent=f.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=M(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,h(Oe==2||Oe==4?r.neg():r,e,t,!0)):new n(NaN)};f.times=f.mul=function(e){var t,r,n,i,o,s,a,l,c,u=this,p=u.constructor,d=u.d,m=(e=new p(e)).d;if(e.s*=u.s,!d||!d[0]||!m||!m[0])return new p(!e.s||d&&!d[0]&&!m||m&&!m[0]&&!d?NaN:!d||!m?e.s/0:e.s*0);for(r=Z(u.e/E)+Z(e.e/E),l=d.length,c=m.length,l<c&&(o=d,d=m,m=o,s=l,l=c,c=s),o=[],s=l+c,n=s;n--;)o.push(0);for(n=c;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+m[n]*d[i-n-1]+t,o[i--]=a%ge|0,t=a/ge|0;o[i]=(o[i]+t)%ge|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=pn(o,r),b?h(e,p.precision,p.rounding):e};f.toBinary=function(e,t){return ki(this,2,e,t)};f.toDecimalPlaces=f.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(se(e,0,Ue),t===void 0?t=n.rounding:se(t,0,8),h(r,e+r.e+1,t))};f.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=be(n,!0):(se(e,0,Ue),t===void 0?t=i.rounding:se(t,0,8),n=h(new i(n),e+1,t),r=be(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=be(i):(se(e,0,Ue),t===void 0?t=o.rounding:se(t,0,8),n=h(new o(i),e+i.e+1,t),r=be(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};f.toFraction=function(e){var t,r,n,i,o,s,a,l,c,u,p,d,m=this,y=m.d,g=m.constructor;if(!y)return new g(m);if(c=r=new g(1),n=l=new g(0),t=new g(n),o=t.e=js(y)-m.e-1,s=o%E,t.d[0]=Q(10,s<0?E+s:s),e==null)e=o>0?t:c;else{if(a=new g(e),!a.isInt()||a.lt(c))throw Error(qe+a);e=a.gt(t)?o>0?t:c:a}for(b=!1,a=new g(J(y)),u=g.precision,g.precision=o=y.length*E*2;p=M(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=c,c=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=M(e.minus(r),n,0,1,1),l=l.plus(i.times(c)),r=r.plus(i.times(n)),l.s=c.s=m.s,d=M(c,n,o,1).minus(m).abs().cmp(M(l,r,o,1).minus(m).abs())<1?[c,n]:[l,r],g.precision=u,b=!0,d};f.toHexadecimal=f.toHex=function(e,t){return ki(this,16,e,t)};f.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:se(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(b=!1,r=M(r,e,0,t,1).times(e),b=!0,h(r)):(e.s=r.s,r=e),r};f.toNumber=function(){return+this};f.toOctal=function(e,t){return ki(this,8,e,t)};f.toPower=f.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,c=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(Q(+a,c));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return h(a,n,o);if(t=Z(e.e/E),t>=e.d.length-1&&(r=c<0?-c:c)<=xp)return i=Bs(l,a,r,n),e.s<0?new l(1).div(i):h(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if((e.d[t]&1)==0&&(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=Q(+a,c),t=r==0||!isFinite(r)?Z(c*(Math.log("0."+J(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(b=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=Ii(e.times(Ve(a,n+r)),n),i.d&&(i=h(i,n+5,1),nr(i.d,n,o)&&(t=n+10,i=h(Ii(e.times(Ve(a,t+r)),t),t+5,1),+J(i.d).slice(n+1,n+15)+1==1e14&&(i=h(i,n+1,0)))),i.s=s,b=!0,l.rounding=o,h(i,n,o))};f.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=be(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(se(e,1,Ue),t===void 0?t=i.rounding:se(t,0,8),n=h(new i(n),e,t),r=be(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toSignificantDigits=f.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(se(e,1,Ue),t===void 0?t=n.rounding:se(t,0,8)),h(new n(r),e,t)};f.toString=function(){var e=this,t=e.constructor,r=be(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};f.truncated=f.trunc=function(){return h(new this.constructor(this),this.e+1,1)};f.valueOf=f.toJSON=function(){var e=this,t=e.constructor,r=be(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function J(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=E-n.length,r&&(o+=$e(r)),o+=n;s=e[t],n=s+"",r=E-n.length,r&&(o+=$e(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function se(e,t,r){if(e!==~~e||e<t||e>r)throw Error(qe+e)}function nr(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=E,i=0):(i=Math.ceil((t+1)/E),t%=E),o=Q(10,E-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==Q(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==Q(10,t-3)-1,s}function on(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=Ci.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function Pp(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/dn(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=mt(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var M=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var c,u,p,d,m,y,g,I,A,C,w,k,me,ce,Qt,U,ne,Ce,K,ct,Jr=n.constructor,ai=n.s==i.s?1:-1,z=n.d,N=i.d;if(!z||!z[0]||!N||!N[0])return new Jr(!n.s||!i.s||(z?N&&z[0]==N[0]:!N)?NaN:z&&z[0]==0||!N?ai*0:ai/0);for(l?(m=1,u=n.e-i.e):(l=ge,m=E,u=Z(n.e/m)-Z(i.e/m)),K=N.length,ne=z.length,A=new Jr(ai),C=A.d=[],p=0;N[p]==(z[p]||0);p++);if(N[p]>(z[p]||0)&&u--,o==null?(ce=o=Jr.precision,s=Jr.rounding):a?ce=o+(n.e-i.e)+1:ce=o,ce<0)C.push(1),y=!0;else{if(ce=ce/m+2|0,p=0,K==1){for(d=0,N=N[0],ce++;(p<ne||d)&&ce--;p++)Qt=d*l+(z[p]||0),C[p]=Qt/N|0,d=Qt%N|0;y=d||p<ne}else{for(d=l/(N[0]+1)|0,d>1&&(N=e(N,d,l),z=e(z,d,l),K=N.length,ne=z.length),U=K,w=z.slice(0,K),k=w.length;k<K;)w[k++]=0;ct=N.slice(),ct.unshift(0),Ce=N[0],N[1]>=l/2&&++Ce;do d=0,c=t(N,w,K,k),c<0?(me=w[0],K!=k&&(me=me*l+(w[1]||0)),d=me/Ce|0,d>1?(d>=l&&(d=l-1),g=e(N,d,l),I=g.length,k=w.length,c=t(g,w,I,k),c==1&&(d--,r(g,K<I?ct:N,I,l))):(d==0&&(c=d=1),g=N.slice()),I=g.length,I<k&&g.unshift(0),r(w,g,k,l),c==-1&&(k=w.length,c=t(N,w,K,k),c<1&&(d++,r(w,K<k?ct:N,k,l))),k=w.length):c===0&&(d++,w=[0]),C[p++]=d,c&&w[0]?w[k++]=z[U]||0:(w=[z[U]],k=1);while((U++<ne||w[0]!==void 0)&&ce--);y=w[0]!==void 0}C[0]||C.shift()}if(m==1)A.e=u,Ls=y;else{for(p=1,d=C[0];d>=10;d/=10)p++;A.e=p+u*m-1,h(A,a?o+A.e+1:o,s,y)}return A}}();function h(e,t,r,n){var i,o,s,a,l,c,u,p,d,m=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=E,s=t,u=p[d=0],l=u/Q(10,i-s-1)%10|0;else if(d=Math.ceil((o+1)/E),a=p.length,d>=a)if(n){for(;a++<=d;)p.push(0);u=l=0,i=1,o%=E,s=o-E+1}else break e;else{for(u=a=p[d],i=1;a>=10;a/=10)i++;o%=E,s=o-E+i,l=s<0?0:u/Q(10,i-s-1)%10|0}if(n=n||t<0||p[d+1]!==void 0||(s<0?u:u%Q(10,i-s-1)),c=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?u/Q(10,i-s):0:p[d-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,c?(t-=e.e+1,p[0]=Q(10,(E-t%E)%E),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=d,a=1,d--):(p.length=d+1,a=Q(10,E-o),p[d]=s>0?(u/Q(10,i-s)%Q(10,s)|0)*a:0),c)for(;;)if(d==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==ge&&(p[0]=1));break}else{if(p[d]+=a,p[d]!=ge)break;p[d--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return b&&(e.e>m.maxE?(e.d=null,e.e=NaN):e.e<m.minE&&(e.e=0,e.d=[0])),e}function be(e,t,r){if(!e.isFinite())return Hs(e);var n,i=e.e,o=J(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+$e(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+$e(-i-1)+o,r&&(n=r-s)>0&&(o+=$e(n))):i>=s?(o+=$e(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+$e(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=$e(n))),o}function pn(e,t){var r=e[0];for(t*=E;r>=10;r/=10)t++;return t}function cn(e,t,r){if(t>bp)throw b=!0,r&&(e.precision=r),Error($s);return h(new e(an),t,1,!0)}function xe(e,t,r){if(t>Si)throw Error($s);return h(new e(ln),t,r,!0)}function js(e){var t=e.length-1,r=t*E+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function $e(e){for(var t="";e--;)t+="0";return t}function Bs(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/E+4);for(b=!1;;){if(r%2&&(o=o.times(t),Ms(o.d,s)&&(i=!0)),r=Z(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),Ms(t.d,s)}return b=!0,o}function _s(e){return e.d[e.d.length-1]&1}function Qs(e,t,r){for(var n,i,o=new e(t[0]),s=0;++s<t.length;){if(i=new e(t[s]),!i.s){o=i;break}n=o.cmp(i),(n===r||n===0&&o.s===r)&&(o=i)}return o}function Ii(e,t){var r,n,i,o,s,a,l,c=0,u=0,p=0,d=e.constructor,m=d.rounding,y=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(b=!1,l=y):l=t,a=new d(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(Q(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new d(1),d.precision=l;;){if(o=h(o.times(e),l,1),r=r.times(++u),a=s.plus(M(o,r,l,1)),J(a.d).slice(0,l)===J(s.d).slice(0,l)){for(i=p;i--;)s=h(s.times(s),l,1);if(t==null)if(c<3&&nr(s.d,l-n,m,c))d.precision=l+=10,r=o=a=new d(1),u=0,c++;else return h(s,d.precision=y,m,b=!0);else return d.precision=y,s}s=a}}function Ve(e,t){var r,n,i,o,s,a,l,c,u,p,d,m=1,y=10,g=e,I=g.d,A=g.constructor,C=A.rounding,w=A.precision;if(g.s<0||!I||!I[0]||!g.e&&I[0]==1&&I.length==1)return new A(I&&!I[0]?-1/0:g.s!=1?NaN:I?0:g);if(t==null?(b=!1,u=w):u=t,A.precision=u+=y,r=J(I),n=r.charAt(0),Math.abs(o=g.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)g=g.times(e),r=J(g.d),n=r.charAt(0),m++;o=g.e,n>1?(g=new A("0."+r),o++):g=new A(n+"."+r.slice(1))}else return c=cn(A,u+2,w).times(o+""),g=Ve(new A(n+"."+r.slice(1)),u-y).plus(c),A.precision=w,t==null?h(g,w,C,b=!0):g;for(p=g,l=s=g=M(g.minus(1),g.plus(1),u,1),d=h(g.times(g),u,1),i=3;;){if(s=h(s.times(d),u,1),c=l.plus(M(s,new A(i),u,1)),J(c.d).slice(0,u)===J(l.d).slice(0,u))if(l=l.times(2),o!==0&&(l=l.plus(cn(A,u+2,w).times(o+""))),l=M(l,new A(m),u,1),t==null)if(nr(l.d,u-y,C,a))A.precision=u+=y,c=s=g=M(p.minus(1),p.plus(1),u,1),d=h(g.times(g),u,1),i=a=1;else return h(l,A.precision=w,C,b=!0);else return A.precision=w,l;l=c,i+=2}}function Hs(e){return String(e.s*e.s/0)}function sn(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%E,r<0&&(n+=E),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=E;n<i;)e.d.push(+t.slice(n,n+=E));t=t.slice(n),n=E-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),b&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function vp(e,t){var r,n,i,o,s,a,l,c,u;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),Us.test(t))return sn(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(wp.test(t))r=16,t=t.toLowerCase();else if(hp.test(t))r=2;else if(Ep.test(t))r=8;else throw Error(qe+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=Bs(n,new n(r),o,o*2)),c=on(t,r,ge),u=c.length-1,o=u;c[o]===0;--o)c.pop();return o<0?new n(e.s*0):(e.e=pn(c,u),e.d=c,b=!1,s&&(e=M(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?Q(2,l):De.pow(2,l))),b=!0,e)}function Tp(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:mt(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/dn(5,r)),t=mt(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function mt(e,t,r,n,i){var o,s,a,l,c=1,u=e.precision,p=Math.ceil(u/E);for(b=!1,l=r.times(r),a=new e(n);;){if(s=M(a.times(l),new e(t++*t++),u,1),a=i?n.plus(s):n.minus(s),n=M(s.times(l),new e(t++*t++),u,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,c++}return b=!0,s.d.length=p+1,s}function dn(e,t){for(var r=e;--t;)r*=e;return r}function Gs(e,t){var r,n=t.s<0,i=xe(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return Oe=n?4:1,t;if(r=t.divToInt(i),r.isZero())Oe=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return Oe=_s(r)?n?2:3:n?4:1,t;Oe=_s(r)?n?1:4:n?3:2}return t.minus(i).abs()}function ki(e,t,r,n){var i,o,s,a,l,c,u,p,d,m=e.constructor,y=r!==void 0;if(y?(se(r,1,Ue),n===void 0?n=m.rounding:se(n,0,8)):(r=m.precision,n=m.rounding),!e.isFinite())u=Hs(e);else{for(u=be(e),s=u.indexOf("."),y?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(u=u.replace(".",""),d=new m(1),d.e=u.length-s,d.d=on(be(d),10,i),d.e=d.d.length),p=on(u,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])u=y?"0p+0":"0";else{if(s<0?o--:(e=new m(e),e.d=p,e.e=o,e=M(e,d,r,n,0,i),p=e.d,o=e.e,c=Ls),s=p[r],a=i/2,c=c||p[r+1]!==void 0,c=n<4?(s!==void 0||c)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||c||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,c)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,u="";s<l;s++)u+=Ci.charAt(p[s]);if(y){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)u+="0";for(p=on(u,i,t),l=p.length;!p[l-1];--l);for(s=1,u="1.";s<l;s++)u+=Ci.charAt(p[s])}else u=u.charAt(0)+"."+u.slice(1);u=u+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)u="0"+u;u="0."+u}else if(++o>l)for(o-=l;o--;)u+="0";else o<l&&(u=u.slice(0,o)+"."+u.slice(o))}u=(t==16?"0x":t==2?"0b":t==8?"0o":"")+u}return e.s<0?"-"+u:u}function Ms(e,t){if(e.length>t)return e.length=t,!0}function Ap(e){return new this(e).abs()}function Cp(e){return new this(e).acos()}function Rp(e){return new this(e).acosh()}function Sp(e,t){return new this(e).plus(t)}function Ip(e){return new this(e).asin()}function kp(e){return new this(e).asinh()}function Op(e){return new this(e).atan()}function Dp(e){return new this(e).atanh()}function Np(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=xe(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?xe(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=xe(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(M(e,t,o,1)),t=xe(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(M(e,t,o,1)),r}function _p(e){return new this(e).cbrt()}function Mp(e){return h(e=new this(e),e.e+1,2)}function Fp(e,t,r){return new this(e).clamp(t,r)}function Lp(e){if(!e||typeof e!="object")throw Error(un+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,Ue,"rounding",0,8,"toExpNeg",-dt,0,"toExpPos",0,dt,"maxE",0,dt,"minE",-dt,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=Ri[r]),(n=e[r])!==void 0)if(Z(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(qe+r+": "+n);if(r="crypto",i&&(this[r]=Ri[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(Vs);else this[r]=!1;else throw Error(qe+r+": "+n);return this}function $p(e){return new this(e).cos()}function Vp(e){return new this(e).cosh()}function Js(e){var t,r,n;function i(o){var s,a,l,c=this;if(!(c instanceof i))return new i(o);if(c.constructor=i,Fs(o)){c.s=o.s,b?!o.d||o.e>i.maxE?(c.e=NaN,c.d=null):o.e<i.minE?(c.e=0,c.d=[0]):(c.e=o.e,c.d=o.d.slice()):(c.e=o.e,c.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){c.s=1/o<0?-1:1,c.e=0,c.d=[0];return}if(o<0?(o=-o,c.s=-1):c.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;b?s>i.maxE?(c.e=NaN,c.d=null):s<i.minE?(c.e=0,c.d=[0]):(c.e=s,c.d=[o]):(c.e=s,c.d=[o]);return}if(o*0!==0){o||(c.s=NaN),c.e=NaN,c.d=null;return}return sn(c,o.toString())}if(l==="string")return(a=o.charCodeAt(0))===45?(o=o.slice(1),c.s=-1):(a===43&&(o=o.slice(1)),c.s=1),Us.test(o)?sn(c,o):vp(c,o);if(l==="bigint")return o<0?(o=-o,c.s=-1):c.s=1,sn(c,o.toString());throw Error(qe+o)}if(i.prototype=f,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=Lp,i.clone=Js,i.isDecimal=Fs,i.abs=Ap,i.acos=Cp,i.acosh=Rp,i.add=Sp,i.asin=Ip,i.asinh=kp,i.atan=Op,i.atanh=Dp,i.atan2=Np,i.cbrt=_p,i.ceil=Mp,i.clamp=Fp,i.cos=$p,i.cosh=Vp,i.div=qp,i.exp=Up,i.floor=jp,i.hypot=Bp,i.ln=Qp,i.log=Hp,i.log10=Jp,i.log2=Gp,i.max=Wp,i.min=Kp,i.mod=zp,i.mul=Yp,i.pow=Zp,i.random=Xp,i.round=ed,i.sign=td,i.sin=rd,i.sinh=nd,i.sqrt=id,i.sub=od,i.sum=sd,i.tan=ad,i.tanh=ld,i.trunc=cd,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function qp(e,t){return new this(e).div(t)}function Up(e){return new this(e).exp()}function jp(e){return h(e=new this(e),e.e+1,3)}function Bp(){var e,t,r=new this(0);for(b=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return b=!0,new this(1/0);r=t}return b=!0,r.sqrt()}function Fs(e){return e instanceof De||e&&e.toStringTag===qs||!1}function Qp(e){return new this(e).ln()}function Hp(e,t){return new this(e).log(t)}function Gp(e){return new this(e).log(2)}function Jp(e){return new this(e).log(10)}function Wp(){return Qs(this,arguments,-1)}function Kp(){return Qs(this,arguments,1)}function zp(e,t){return new this(e).mod(t)}function Yp(e,t){return new this(e).mul(t)}function Zp(e,t){return new this(e).pow(t)}function Xp(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:se(e,1,Ue),n=Math.ceil(e/E),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(Vs);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=E,n&&e&&(i=Q(10,E-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=E)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<E&&(r-=E-n)}return s.e=r,s.d=a,s}function ed(e){return h(e=new this(e),e.e+1,this.rounding)}function td(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function rd(e){return new this(e).sin()}function nd(e){return new this(e).sinh()}function id(e){return new this(e).sqrt()}function od(e,t){return new this(e).sub(t)}function sd(){var e=0,t=arguments,r=new this(t[e]);for(b=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return b=!0,h(r,this.precision,this.rounding)}function ad(e){return new this(e).tan()}function ld(e){return new this(e).tanh()}function cd(e){return h(e=new this(e),e.e+1,1)}f[Symbol.for("nodejs.util.inspect.custom")]=f.toString;f[Symbol.toStringTag]="Decimal";var De=f.constructor=Js(Ri);an=new De(an);ln=new De(ln);var ae=De;function ft(e){return De.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var mn={};Gt(mn,{ModelAction:()=>gt,datamodelEnumToSchemaEnum:()=>ud});function ud(e){return{name:e.name,values:e.values.map(t=>t.name)}}var gt=(w=>(w.findUnique="findUnique",w.findUniqueOrThrow="findUniqueOrThrow",w.findFirst="findFirst",w.findFirstOrThrow="findFirstOrThrow",w.findMany="findMany",w.create="create",w.createMany="createMany",w.createManyAndReturn="createManyAndReturn",w.update="update",w.updateMany="updateMany",w.updateManyAndReturn="updateManyAndReturn",w.upsert="upsert",w.delete="delete",w.deleteMany="deleteMany",w.groupBy="groupBy",w.count="count",w.aggregate="aggregate",w.findRaw="findRaw",w.aggregateRaw="aggregateRaw",w))(gt||{});var Ys=fe(ys());import gd from"node:fs";var Ws={keyword:Se,entity:Se,value:e=>X(ze(e)),punctuation:ze,directive:Se,function:Se,variable:e=>X(ze(e)),string:e=>X(Jt(e)),boolean:Ke,number:Se,comment:Wt};var pd=e=>e,fn={},dd=0,T={manual:fn.Prism&&fn.Prism.manual,disableWorkerMessageHandler:fn.Prism&&fn.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof ye){let t=e;return new ye(t.type,T.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(T.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++dd}),e.__id},clone:function e(t,r){let n,i,o=T.util.type(t);switch(r=r||{},o){case"Object":if(i=T.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=T.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=T.util.clone(T.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||T.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,T.languages.DFS(T.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=T.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=T.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return T.hooks.run("before-tokenize",n),n.tokens=T.tokenize(n.code,n.grammar),T.hooks.run("after-tokenize",n),ye.stringify(T.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let g in r){if(!r.hasOwnProperty(g)||!r[g])continue;if(g==s)return;let I=r[g];I=T.util.type(I)==="Array"?I:[I];for(let A=0;A<I.length;++A){let C=I[A],w=C.inside,k=!!C.lookbehind,me=!!C.greedy,ce=0,Qt=C.alias;if(me&&!C.pattern.global){let U=C.pattern.toString().match(/[imuy]*$/)[0];C.pattern=RegExp(C.pattern.source,U+"g")}C=C.pattern||C;for(let U=n,ne=i;U<t.length;ne+=t[U].length,++U){let Ce=t[U];if(t.length>e.length)return;if(Ce instanceof ye)continue;if(me&&U!=t.length-1){C.lastIndex=ne;var p=C.exec(e);if(!p)break;var u=p.index+(k?p[1].length:0),d=p.index+p[0].length,a=U,l=ne;for(let N=t.length;a<N&&(l<d||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,u>=l&&(++U,ne=l);if(t[U]instanceof ye)continue;c=a-U,Ce=e.slice(ne,l),p.index-=ne}else{C.lastIndex=0;var p=C.exec(Ce),c=1}if(!p){if(o)break;continue}k&&(ce=p[1]?p[1].length:0);var u=p.index+ce,p=p[0].slice(ce),d=u+p.length,m=Ce.slice(0,u),y=Ce.slice(d);let K=[U,c];m&&(++U,ne+=m.length,K.push(m));let ct=new ye(g,w?T.tokenize(p,w):p,Qt,p,me);if(K.push(ct),y&&K.push(y),Array.prototype.splice.apply(t,K),c!=1&&T.matchGrammar(e,t,r,U,ne,!0,g),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return T.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=T.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=T.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:ye};T.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};T.languages.javascript=T.languages.extend("clike",{"class-name":[T.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});T.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;T.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:T.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:T.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:T.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:T.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});T.languages.markup&&T.languages.markup.tag.addInlined("script","javascript");T.languages.js=T.languages.javascript;T.languages.typescript=T.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});T.languages.ts=T.languages.typescript;function ye(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}ye.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return ye.stringify(r,t)}).join(""):md(e.type)(e.content)};function md(e){return Ws[e]||pd}function Ks(e){return fd(e,T.languages.javascript)}function fd(e,t){return T.tokenize(e,t).map(n=>ye.stringify(n)).join("")}function zs(e){return mi(e)}var gn=class e{firstLineNumber;lines;static read(t){let r;try{r=gd.readFileSync(t,"utf-8")}catch{return null}return e.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new e(1,r)}constructor(t,r){this.firstLineNumber=t,this.lines=r}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new e(this.firstLineNumber,i)}mapLines(t){return new e(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new e(t,zs(n).split(`
`))}highlight(){let t=Ks(this.toString());return new e(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var yd={red:Re,gray:Wt,dim:We,bold:X,underline:ie,highlightSource:e=>e.highlight()},hd={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function wd({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function Ed({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s=wd({message:t,originalMethod:r,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),c=gn.read(a.fileName)?.slice(l,a.lineNumber),u=c?.lineAt(a.lineNumber);if(c&&u){let p=bd(u),d=xd(u);if(!d)return s;s.functionName=`${d.code})`,s.location=a,n||(c=c.mapLineAt(a.lineNumber,y=>y.slice(0,d.openingBraceIndex))),c=o.highlightSource(c);let m=String(c.lastLineNumber).length;if(s.contextLines=c.mapLines((y,g)=>o.gray(String(g).padStart(m))+" "+y).mapLines(y=>o.dim(y)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let y=p+m+1;y+=2,s.callArguments=(0,Ys.default)(i,y).slice(y)}}return s}function xd(e){let t=Object.keys(gt).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function bd(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function Pd({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(vd(t))),i){a.push("");let c=[i.toString()];o&&(c.push(o),c.push(s.dim(")"))),a.push(c.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function vd(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function yn(e){let t=e.showColors?yd:hd,r;return r=Ed(e,t),Pd(r,t)}var sa=fe(Oi());function ta(e,t,r){let n=ra(e),i=Td(n),o=Cd(i);o?hn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function ra(e){return e.errors.flatMap(t=>t.kind==="Union"?ra(t):[t])}function Td(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:Ad(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function Ad(e,t){return[...new Set(e.concat(t))]}function Cd(e){return Ti(e,(t,r)=>{let n=Xs(t),i=Xs(r);return n!==i?n-i:ea(t)-ea(r)})}function Xs(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function ea(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var ue=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};ia();var yt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};na();var wn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};var En=e=>e,xn={bold:En,red:En,green:En,dim:En,enabled:!1},oa={bold:X,red:Re,green:Jt,dim:We,enabled:!0},ht={write(e){e.writeLine(",")}};var Pe=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var je=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var wt=class extends je{items=[];addItem(t){return this.items.push(new wn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Pe("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(ht,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Et=class e extends je{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof wt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Pe("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(ht,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};var G=class extends je{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Pe(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};var ir=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(ht,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function hn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":Rd(e,t);break;case"IncludeOnScalar":Sd(e,t);break;case"EmptySelection":Id(e,t,r);break;case"UnknownSelectionField":Nd(e,t);break;case"InvalidSelectionValue":_d(e,t);break;case"UnknownArgument":Md(e,t);break;case"UnknownInputField":Fd(e,t);break;case"RequiredArgumentMissing":Ld(e,t);break;case"InvalidArgumentType":$d(e,t);break;case"InvalidArgumentValue":Vd(e,t);break;case"ValueTooLarge":qd(e,t);break;case"SomeFieldsMissing":Ud(e,t);break;case"TooManyFieldsGiven":jd(e,t);break;case"Union":ta(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function Rd(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function Sd(e,t){let[r,n]=xt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ue(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${or(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function Id(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){kd(e,t,i);return}if(n.hasField("select")){Od(e,t);return}}if(r?.[Le(e.outputType.name)]){Dd(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function kd(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new ue(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Od(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),ca(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${or(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function Dd(e,t){let r=new ir;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ue("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=xt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let l=a?.value.asObject()??new Et;l.addSuggestion(n),a.value=l}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function Nd(e,t){let r=ua(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":ca(n,e.outputType);break;case"include":Bd(n,e.outputType);break;case"omit":Qd(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(or(n)),i.join(" ")})}function _d(e,t){let r=ua(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Md(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),Hd(n,e.arguments)),t.addErrorMessage(i=>aa(i,r,e.arguments.map(o=>o.name)))}function Fd(e,t){let[r,n]=xt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&pa(o,e.inputType)}t.addErrorMessage(o=>aa(o,n,e.inputType.fields.map(s=>s.name)))}function aa(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Jd(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(or(e)),n.join(" ")}function Ld(e,t){let r;t.addErrorMessage(l=>r?.value instanceof G&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=xt(e.argumentPath),s=new ir,a=n.getDeepFieldValue(i)?.asObject();if(a){if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new ue(o,s).makeRequired())}else{let l=e.inputTypes.map(la).join(" | ");a.addSuggestion(new ue(o,l).makeRequired())}if(e.dependentArgumentPath){n.getDeepField(e.dependentArgumentPath)?.markAsError();let[,l]=xt(e.dependentArgumentPath);t.addErrorMessage(c=>`Argument \`${c.green(o)}\` is required because argument \`${c.green(l)}\` was provided.`)}}}function la(e){return e.kind==="list"?`${la(e.elementType)}[]`:e.name}function $d(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=bn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Vd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=bn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function qd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof G&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Ud(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&pa(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${bn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(or(i)),o.join(" ")})}function jd(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${bn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function ca(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ue(r.name,"true"))}function Bd(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ue(r.name,"true"))}function Qd(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new ue(r.name,"true"))}function Hd(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ue(r.name,r.typeNames.join(" | ")))}function ua(e,t){let[r,n]=xt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),l=o?.getField(n);return o&&l?{parentKind:"select",parent:o,field:l,fieldName:n}:(l=s?.getField(n),s&&l?{parentKind:"include",field:l,parent:s,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"omit",field:l,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function pa(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ue(r.name,r.typeNames.join(" | ")))}function xt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function or({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function bn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Gd=3;function Jd(e,t){let r=1/0,n;for(let i of t){let o=(0,sa.default)(e,i);o>Gd||o<r&&(r=o,n=i)}return n}var sr=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function bt(e){return e instanceof sr}var Pn=Symbol(),Ni=new WeakMap,Ne=class{constructor(t){t===Pn?Ni.set(this,`Prisma.${this._getName()}`):Ni.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Ni.get(this)}},ar=class extends Ne{_getNamespace(){return"NullTypes"}},lr=class extends ar{#t};Mi(lr,"DbNull");var cr=class extends ar{#t};Mi(cr,"JsonNull");var ur=class extends ar{#t};Mi(ur,"AnyNull");var _i={classes:{DbNull:lr,JsonNull:cr,AnyNull:ur},instances:{DbNull:new lr(Pn),JsonNull:new cr(Pn),AnyNull:new ur(Pn)}};function Mi(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var da=": ",vn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+da.length}write(t){let r=new Pe(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(da).write(this.value)}};var Fi=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function Pt(e){return new Fi(ma(e))}function ma(e){let t=new Et;for(let[r,n]of Object.entries(e)){let i=new vn(r,fa(n));t.addField(i)}return t}function fa(e){if(typeof e=="string")return new G(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new G(String(e));if(typeof e=="bigint")return new G(`${e}n`);if(e===null)return new G("null");if(e===void 0)return new G("undefined");if(ft(e))return new G(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new G(`Buffer.alloc(${e.byteLength})`):new G(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=nn(e)?e.toISOString():"Invalid Date";return new G(`new Date("${t}")`)}return e instanceof Ne?new G(`Prisma.${e._getName()}`):bt(e)?new G(`prisma.${Le(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Wd(e):typeof e=="object"?ma(e):new G(Object.prototype.toString.call(e))}function Wd(e){let t=new wt;for(let r of e)t.addItem(fa(r));return t}function Tn(e,t){let r=t==="pretty"?oa:xn,n=e.renderAllMessages(r),i=new yt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function An({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=Pt(e);for(let p of t)hn(p,a,s);let{message:l,args:c}=Tn(a,r),u=yn({message:l,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:c});throw new ee(u,{clientVersion:o})}function ve(e){return e.replace(/^./,t=>t.toLowerCase())}function ya(e,t,r){let n=ve(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Kd({...e,...ga(t.name,e,t.result.$allModels),...ga(t.name,e,t.result[n])})}function Kd(e){let t=new Ee,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return tn(e,n=>({...n,needs:r(n.name,new Set)}))}function ga(e,t,r){return r?tn(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:zd(t,o,i)})):{}}function zd(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function ha(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function wa(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var Cn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Ee;modelExtensionsCache=new Ee;queryCallbacksCache=new Ee;clientExtensions=rr(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=rr(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>ya(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=ve(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},vt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new Cn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new Cn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var Rn=class{constructor(t){this.name=t}};function Ea(e){return e instanceof Rn}function Yd(e){return new Rn(e)}var xa=Symbol(),pr=class{constructor(t){if(t!==xa)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?Li:t}},Li=new pr(xa);function Te(e){return e instanceof pr}var Zd={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},ba="explicitly `undefined` values are not allowed";function Vi({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=vt.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:l,previewFeatures:c,globalOmit:u}){let p=new $i({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l,previewFeatures:c,globalOmit:u});return{modelName:e,action:Zd[t],query:dr(r,p)}}function dr({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:va(r,n),selection:Xd(e,t,i,n)}}function Xd(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),nm(e,n)):em(n,t,r)}function em(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&tm(n,t,e),rm(n,r,e),n}function tm(e,t,r){for(let[n,i]of Object.entries(t)){if(Te(i))continue;let o=r.nestSelection(n);if(qi(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=dr(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=dr(i,o)}}function rm(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=wa(i,n);for(let[s,a]of Object.entries(o)){if(Te(a))continue;qi(a,r.nestSelection(s));let l=r.findField(s);n?.[s]&&!l||(e[s]=!a)}}function nm(e,t){let r={},n=t.getComputedFields(),i=ha(e,n);for(let[o,s]of Object.entries(i)){if(Te(s))continue;let a=t.nestSelection(o);qi(s,a);let l=t.findField(o);if(!(n?.[o]&&!l)){if(s===!1||s===void 0||Te(s)){r[o]=!1;continue}if(s===!0){l?.kind==="object"?r[o]=dr({},a):r[o]=!0;continue}r[o]=dr(s,a)}}return r}function Pa(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(pt(e)){if(nn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(Ea(e))return{$type:"Param",value:e.name};if(bt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return im(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:Buffer.from(r,n,i).toString("base64")}}if(om(e))return e.values;if(ft(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Ne){if(e!==_i.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(sm(e))return e.toJSON();if(typeof e=="object")return va(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function va(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);Te(i)||(i!==void 0?r[n]=Pa(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:ba}))}return r}function im(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||Te(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(Pa(o,i))}return r}function om(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function sm(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function qi(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:ba})}var $i=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){An({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Le(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:Ie(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function Ta(e){if(!e._hasPreviewFlag("metrics"))throw new ee("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var mr=class{_client;constructor(t){this._client=t}prometheus(t){return Ta(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return Ta(this._client),this._client._engine.metrics({format:"json",...t})}};function am(e,t){let r=rr(()=>lm(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function lm(e){return{datamodel:{models:Ui(e.models),enums:Ui(e.enums),types:Ui(e.types)}}}function Ui(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}var ji=new WeakMap,Sn="$$PrismaTypedSql",fr=class{constructor(t,r){ji.set(this,{sql:t,values:r}),Object.defineProperty(this,Sn,{value:Sn})}get sql(){return ji.get(this).sql}get values(){return ji.get(this).values}};function cm(e){return(...t)=>new fr(e,t)}function In(e){return e!=null&&e[Sn]===Sn}var yu=fe(di());import{AsyncResource as uy}from"node:async_hooks";import{EventEmitter as py}from"node:events";import dy from"node:fs";import Go from"node:path";var pe=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function um(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new pe([r,...Array(e.length-1).fill(t),n],e)}function Aa(e){return new pe([e],[])}var pm=Aa("");function Ca(e,...t){return new pe(e,t)}function gr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function te(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}function Ye(e){let t=new Ee;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var kn={enumerable:!0,configurable:!0,writable:!0};function On(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>kn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var Ra=Symbol.for("nodejs.util.inspect.custom");function he(e,t){let r=dm(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=Sa(Reflect.ownKeys(o),r),a=Sa(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...kn,...l?.getPropertyDescriptor(s)}:kn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[Ra]=function(){let o={...this};return delete o[Ra],o},i}function dm(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function Sa(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function Tt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function At(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function Ia(e){if(e===void 0)return"";let t=Pt(e);return new yt(0,{colors:xn}).write(t).toString()}var mm="P2037";function Dn({error:e,user_facing_error:t},r,n){return t.error_code?new B(fm(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new Y(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function fm(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===mm&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}var yr="<unknown>";function ka(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=hm(n)||Em(n)||Pm(n)||Cm(n)||Tm(n);return i&&r.push(i),r},[])}var gm=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,ym=/\((\S*)(?::(\d+))(?::(\d+))\)/;function hm(e){var t=gm.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=ym.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||yr,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var wm=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function Em(e){var t=wm.exec(e);return t?{file:t[2],methodName:t[1]||yr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var xm=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,bm=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function Pm(e){var t=xm.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=bm.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||yr,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var vm=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function Tm(e){var t=vm.exec(e);return t?{file:t[3],methodName:t[1]||yr,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var Am=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function Cm(e){var t=Am.exec(e);return t?{file:t[2],methodName:t[1]||yr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Bi=class{getLocation(){return null}},Qi=class{_error;constructor(){this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=ka(t).find(i=>{if(!i.file)return!1;let o=hi(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function Be(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Bi:new Qi}var Oa={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Ct(e={}){let t=Sm(e);return Object.entries(t).reduce((n,[i,o])=>(Oa[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function Sm(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Nn(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function Da(e,t){let r=Nn(e);return t({action:"aggregate",unpacker:r,argsMapper:Ct})(e)}function Im(e={}){let{select:t,...r}=e;return typeof t=="object"?Ct({...r,_count:t}):Ct({...r,_count:{_all:!0}})}function km(e={}){return typeof e.select=="object"?t=>Nn(e)(t)._count:t=>Nn(e)(t)._count._all}function Na(e,t){return t({action:"count",unpacker:km(e),argsMapper:Im})(e)}function Om(e={}){let t=Ct(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function Dm(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function _a(e,t){return t({action:"groupBy",unpacker:Dm(e),argsMapper:Om})(e)}function Ma(e,t,r){if(t==="aggregate")return n=>Da(n,r);if(t==="count")return n=>Na(n,r);if(t==="groupBy")return n=>_a(n,r)}function Fa(e,t){let r=t.fields.filter(i=>!i.relationName),n=Ns(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new sr(e,o,s.type,s.isList,s.kind==="enum")},...On(Object.keys(n))})}var La=e=>Array.isArray(e)?e:e.split("."),Hi=(e,t)=>La(t).reduce((r,n)=>r&&r[n],e),$a=(e,t,r)=>La(t).reduceRight((n,i,o,s)=>Object.assign({},Hi(e,s.slice(0,o)),{[i]:n}),r);function Nm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function _m(e,t,r){return t===void 0?e??{}:$a(t,r,e||!0)}function Gi(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,c)=>({...l,[c.name]:c}),{});return l=>{let c=Be(e._errorFormat),u=Nm(n,i),p=_m(l,o,u),d=r({dataPath:u,callsite:c})(p),m=Mm(e,t);return new Proxy(d,{get(y,g){if(!m.includes(g))return y[g];let A=[a[g].type,r,g],C=[u,p];return Gi(e,...A,...C)},...On([...m,...Object.getOwnPropertyNames(d)])})}}function Mm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var Fm=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Lm=["aggregate","count","groupBy"];function Ji(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[$m(e,t),qm(e,t),gr(r),te("name",()=>t),te("$name",()=>t),te("$parent",()=>e._appliedParent)];return he({},n)}function $m(e,t){let r=ve(t),n=Object.keys(gt).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>l=>{let c=Be(e._errorFormat);return e._createPrismaPromise(u=>{let p={args:l,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:u,callsite:c};return e._request({...p,...a})},{action:o,args:l,model:t})};return Fm.includes(o)?Gi(e,t,s):Vm(i)?Ma(e,i,s):s({})}}}function Vm(e){return Lm.includes(e)}function qm(e,t){return Ye(te("fields",()=>{let r=e._runtimeDataModel.models[t];return Fa(t,r)}))}function Va(e){return e.replace(/^./,t=>t.toUpperCase())}var Wi=Symbol();function hr(e){let t=[Um(e),jm(e),te(Wi,()=>e),te("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(gr(r)),he(e,t)}function Um(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function jm(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(ve),n=[...new Set(t.concat(r))];return Ye({getKeys(){return n},getPropertyValue(i){let o=Va(i);if(e._runtimeDataModel.models[o]!==void 0)return Ji(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Ji(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function qa(e){return e[Wi]?e[Wi]:e}function Ua(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$on:{value:void 0}});return hr(t)}function ja({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let l of Object.values(o)){if(n){if(n[l.name])continue;let c=l.needs.filter(u=>n[u]);c.length>0&&a.push(Tt(c))}else if(r){if(!r[l.name])continue;let c=l.needs.filter(u=>!r[u]);c.length>0&&a.push(Tt(c))}Bm(e,l.needs)&&s.push(Qm(l,he(e,s)))}return s.length>0||a.length>0?he(e,[...s,...a]):e}function Bm(e,t){return t.every(r=>vi(e,r))}function Qm(e,t){return Ye(te(e.name,()=>e.compute(t)))}function _n({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=_n({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Ba({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Ba({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Ba({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||Te(s))continue;let l=n.models[r].fields.find(u=>u.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let c=typeof s=="object"?s:{};t[o]=_n({visitor:i,result:t[o],args:c,modelName:l.type,runtimeDataModel:n})}}function Qa({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:_n({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,l,c)=>{let u=ve(l);return ja({result:a,modelName:u,select:c.select,omit:c.select?void 0:{...o?.[u],...c.omit},extensions:n})}})}var Hm=["$connect","$disconnect","$on","$transaction","$extends"],Ha=Hm;function Ga(e){if(e instanceof pe)return Gm(e);if(In(e))return Jm(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=wr(e[n]);return r}let t={};for(let r in e)t[r]=wr(e[r]);return t}function Gm(e){return new pe(e.strings,e.values)}function Jm(e){return new fr(e.sql,e.values)}function wr(e){if(typeof e!="object"||e==null||e instanceof Ne||bt(e))return e;if(ft(e))return new ae(e.toFixed());if(pt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=wr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:wr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=wr(e[r]);return t}Ie(e,"Unknown value")}function Wa(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Ga(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=Za(o,l),a.args=s,Wa(e,a,r,n+1)}})})}function Ka(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return Wa(e,t,s)}function za(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Ya(r,n,0,e):e(r)}}function Ya(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=Za(i,l),Ya(a,t,r+1,n)}})}var Ja=e=>e;function Za(e=Ja,t=Ja){return r=>e(t(r))}var Xa=q("prisma:client"),el={Vercel:"vercel","Netlify CI":"netlify"};function tl({postinstall:e,ciName:t,clientVersion:r}){if(Xa("checkPlatformCaching:postinstall",e),Xa("checkPlatformCaching:ciName",t),e===!0&&t&&t in el){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${el[t]}-build`;throw console.error(n),new _(n,r)}}function rl(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}var Wm=()=>globalThis.process?.release?.name==="node",Km=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,zm=()=>!!globalThis.Deno,Ym=()=>typeof globalThis.Netlify=="object",Zm=()=>typeof globalThis.EdgeRuntime=="object",Xm=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function ef(){return[[Ym,"netlify"],[Zm,"edge-light"],[Xm,"workerd"],[zm,"deno"],[Km,"bun"],[Wm,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var tf={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function rf(){let e=ef();return{id:e,prettyName:tf[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var Ki=fe(yi());function nl(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}function il(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var ol=fe(ks());function sl({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,ol.default)({user:t,repo:r,template:n,title:e,body:i})}function al({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=ss(6e3-(s?.length??0)),l=il((0,Ki.default)(a)),c=n?`# Description
\`\`\`
${n}
\`\`\``:"",u=(0,Ki.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${c}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?nl(s):""}
\`\`\`
`),p=sl({title:r,body:u});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${ie(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}function O(e,t){throw new Error(t)}function zi(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>zi(e[r],t[r]))}function Rt(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]==typeof t[o]&&typeof e[o]!="object")return e[o]===t[o];if(ae.isDecimal(e[o])||ae.isDecimal(t[o])){let s=ll(e[o]),a=ll(t[o]);return s&&a&&s.equals(a)}else if(e[o]instanceof Uint8Array||t[o]instanceof Uint8Array){let s=cl(e[o]),a=cl(t[o]);return s&&a&&s.equals(a)}else{if(e[o]instanceof Date||t[o]instanceof Date)return ul(e[o])?.getTime()===ul(t[o])?.getTime();if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return pl(e[o])===pl(t[o]);if(typeof e[o]=="number"||typeof t[o]=="number")return dl(e[o])===dl(t[o])}return zi(e[o],t[o])})}function ll(e){return ae.isDecimal(e)?e:typeof e=="number"||typeof e=="string"?new ae(e):void 0}function cl(e){return Buffer.isBuffer(e)?e:e instanceof Uint8Array?Buffer.from(e.buffer,e.byteOffset,e.byteLength):typeof e=="string"?Buffer.from(e,"base64"):void 0}function ul(e){return e instanceof Date?e:typeof e=="string"||typeof e=="number"?new Date(e):void 0}function pl(e){return typeof e=="bigint"?e:typeof e=="number"||typeof e=="string"?BigInt(e):void 0}function dl(e){return typeof e=="number"?e:typeof e=="string"?Number(e):void 0}function Er(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?Buffer.from(r).toString("base64"):r)}function nf(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function of(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function Ze(e){return e===null?e:Array.isArray(e)?e.map(Ze):typeof e=="object"?nf(e)?sf(e):e.constructor!==null&&e.constructor.name!=="Object"?e:of(e,Ze):e}function sf({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new De(t);case"Json":return JSON.parse(t);default:O(t,"Unknown tagged value")}}var re=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function St(e){if(!Kr(e))throw e;let t=af(e),r=ml(e);throw!t||!r?e:new re(r,t,{driverAdapterError:e})}function Zi(e){throw Kr(e)?new re(`Raw query failed. Code: \`${e.cause.originalCode??"N/A"}\`. Message: \`${e.cause.originalMessage??ml(e)}\``,"P2010",{driverAdapterError:e}):e}function af(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseNotReachable":return"P1001";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TlsConnectionError":return"P1011";case"ConnectionClosed":return"P1017";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:O(e.cause,`Unknown error: ${e.cause}`)}}function ml(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseNotReachable":{let t=e.cause.host&&e.cause.port?`${e.cause.host}:${e.cause.port}`:e.cause.host;return`Can't reach database server${t?` at ${t}`:""}`}case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TlsConnectionError":return`Error opening a TLS connection: ${e.cause.reason}`;case"ConnectionClosed":return"Server has closed the connection.";case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${Yi(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${Yi(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${Yi(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type: ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Error in connector: Conversion error: ${e.cause.level}`;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:O(e.cause,`Unknown error: ${e.cause}`)}}function Yi(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}function fl(e,t){let r=e.map(i=>t.keys.reduce((o,s)=>(o[s]=Ze(i[s]),o),{})),n=new Set(t.nestedSelection);return t.arguments.map(i=>{let o=r.findIndex(s=>Rt(s,i));if(o===-1)return t.expectNonEmpty?new re("An operation failed because it depends on one or more records that were required but not found","P2025"):null;{let s=Object.entries(e[o]).filter(([a])=>n.has(a));return Object.fromEntries(s)}})}var $=class extends Error{name="DataMapperError"};function yl(e,t,r){switch(t.type){case"affectedRows":if(typeof e!="number")throw new $(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"object":return eo(e,t.fields,r,t.skipNulls);case"field":return Xi(e,"<result>",t.fieldType,r);default:O(t,`Invalid data mapping type: '${t.type}'`)}}function eo(e,t,r,n){if(e===null)return null;if(Array.isArray(e)){let i=e;return n&&(i=i.filter(o=>o!==null)),i.map(o=>gl(o,t,r))}if(typeof e=="object")return gl(e,t,r);if(typeof e=="string"){let i;try{i=JSON.parse(e)}catch(o){throw new $("Expected an array or object, got a string that is not valid JSON",{cause:o})}return eo(i,t,r,n)}throw new $(`Expected an array or an object, got: ${typeof e}`)}function gl(e,t,r){if(typeof e!="object")throw new $(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"affectedRows":throw new $(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"object":{if(o.serializedName!==null&&!Object.hasOwn(e,o.serializedName))throw new $(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.serializedName!==null?e[o.serializedName]:e;n[i]=eo(s,o.fields,r,o.skipNulls);break}case"field":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=lf(e[s],s,o.fieldType,r);else throw new $(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:O(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function lf(e,t,r,n){return e===null?r.arity==="list"?[]:null:r.arity==="list"?e.map((o,s)=>Xi(o,`${t}[${s}]`,r,n)):Xi(e,t,r,n)}function Xi(e,t,r,n){switch(r.type){case"unsupported":return e;case"string":{if(typeof e!="string")throw new $(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new $(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new $(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new $(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"bigint":{if(typeof e!="number"&&typeof e!="string")throw new $(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new $(`Expected a float in column '${t}', got string: ${e}`);return i}throw new $(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new $(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}if(Array.isArray(e)){for(let i of e)if(i!==0)return!0;return!1}throw new $(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"decimal":if(typeof e!="number"&&typeof e!="string"&&!ae.isDecimal(e))throw new $(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"datetime":{if(typeof e=="string")return{$type:"DateTime",value:uf(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new $(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"object":return{$type:"Json",value:Er(e)};case"json":return{$type:"Json",value:`${e}`};case"bytes":{switch(r.encoding){case"base64":if(typeof e!="string")throw new $(`Expected a base64-encoded byte array in column '${t}', got ${typeof e}: ${e}`);return{$type:"Bytes",value:e};case"hex":if(typeof e!="string"||!e.startsWith("\\x"))throw new $(`Expected a hex-encoded byte array in column '${t}', got ${typeof e}: ${e}`);return{$type:"Bytes",value:Buffer.from(e.slice(2),"hex").toString("base64")};case"array":if(Array.isArray(e))return{$type:"Bytes",value:Buffer.from(e).toString("base64")};if(e instanceof Uint8Array)return{$type:"Bytes",value:Buffer.from(e).toString("base64")};throw new $(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`);default:O(r.encoding,`DataMapper: Unknown bytes encoding: ${r.encoding}`)}break}case"enum":{let i=n[r.name];if(i===void 0)throw new $(`Unknown enum '${r.name}'`);let o=i[`${e}`];if(o===void 0)throw new $(`Value '${e}' not found in enum '${r.name}'`);return o}default:O(r,`DataMapper: Unknown result type: ${r.type}`)}}var cf=/\d{2}:\d{2}:\d{2}(?:\.\d+)?(Z|[+-]\d{2}(:?\d{2})?)?$/;function uf(e){let t=cf.exec(e);if(t===null)return`${e}Z`;let r=e,[n,i,o]=t;return i!==void 0&&i!=="Z"&&o===void 0?r=`${e}:00`:i===void 0&&(r=`${e}Z`),n.length===e.length?`1970-01-01T${r}`:r}var xr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(xr||(xr={}));function pf(e){switch(e){case"postgresql":case"postgres":case"prisma+postgres":return"postgresql";case"sqlserver":return"mssql";case"mysql":case"sqlite":case"cockroachdb":case"mongodb":return e;default:O(e,`Unknown provider: ${e}`)}}async function Mn({query:e,tracingHelper:t,provider:r,onQuery:n,execute:i}){return await t.runInChildSpan({name:"db_query",kind:xr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":pf(r)}},async()=>{let o=new Date,s=performance.now(),a=await i(),l=performance.now();return n?.({timestamp:o,duration:l-s,query:e.sql,params:e.args}),a})}function Xe(e,t){var r="000000000"+e;return r.substr(r.length-t)}import df from"node:os";function mf(){try{return df.hostname()}catch{return process.env._CLUSTER_NETWORK_NAME_||process.env.COMPUTERNAME||"hostname"}}var hl=2,ff=Xe(process.pid.toString(36),hl),wl=mf(),gf=wl.length,yf=Xe(wl.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+gf+36).toString(36),hl);function to(){return ff+yf}function Fn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function ro(e){let n=Math.pow(36,4),i=0;function o(){return Xe((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var l="c",c=new Date().getTime().toString(36),u=Xe(s().toString(36),4),p=e(),d=o()+o();return l+c+u+p+d}return a.fingerprint=e,a.isCuid=Fn,a}var hf=ro(to);var El=hf;var hc=fe(cc());import{webcrypto as pc}from"node:crypto";var uc="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var ng=128,tt,Ot;function ig(e){!tt||tt.length<e?(tt=Buffer.allocUnsafe(e*ng),pc.getRandomValues(tt),Ot=0):Ot+e>tt.length&&(pc.getRandomValues(tt),Ot=0),Ot+=e}function po(e=21){ig(e|=0);let t="";for(let r=Ot-e;r<Ot;r++)t+=uc[tt[r]&63];return t}import qn from"node:crypto";var mc="0123456789ABCDEFGHJKMNPQRSTVWXYZ",Cr=32;var og=16,fc=10,dc=0xffffffffffff;var rt;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(rt||(rt={}));var nt=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function sg(e){let t=Math.floor(e()*Cr);return t===Cr&&(t=Cr-1),mc.charAt(t)}function ag(e){let t=lg(),r=t&&(t.crypto||t.msCrypto)||(typeof qn<"u"?qn:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(qn?.randomBytes)return()=>qn.randomBytes(1).readUInt8()/255;throw new nt(rt.PRNGDetectFailure,"Failed to find a reliable PRNG")}function lg(){return pg()?self:typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:null}function cg(e,t){let r="";for(;e>0;e--)r=sg(t)+r;return r}function ug(e,t=fc){if(isNaN(e))throw new nt(rt.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>dc)throw new nt(rt.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${dc}: ${e}`);if(e<0)throw new nt(rt.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new nt(rt.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%Cr,n=mc.charAt(r)+n,e=(e-r)/Cr;return n}function pg(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function gc(e,t){let r=t||ag(),n=!e||isNaN(e)?Date.now():e;return ug(n,fc)+cg(og,r)}var W=[];for(let e=0;e<256;++e)W.push((e+256).toString(16).slice(1));function Un(e,t=0){return(W[e[t+0]]+W[e[t+1]]+W[e[t+2]]+W[e[t+3]]+"-"+W[e[t+4]]+W[e[t+5]]+"-"+W[e[t+6]]+W[e[t+7]]+"-"+W[e[t+8]]+W[e[t+9]]+"-"+W[e[t+10]]+W[e[t+11]]+W[e[t+12]]+W[e[t+13]]+W[e[t+14]]+W[e[t+15]]).toLowerCase()}import{randomFillSync as dg}from"node:crypto";var Bn=new Uint8Array(256),jn=Bn.length;function Dt(){return jn>Bn.length-16&&(dg(Bn),jn=0),Bn.slice(jn,jn+=16)}import{randomUUID as mg}from"node:crypto";var mo={randomUUID:mg};function fg(e,t,r){if(mo.randomUUID&&!t&&!e)return mo.randomUUID();e=e||{};let n=e.random??e.rng?.()??Dt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return Un(n)}var fo=fg;var go={};function gg(e,t,r){let n;if(e)n=yc(e.random??e.rng?.()??Dt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Dt();yg(go,i,o),n=yc(o,go.msecs,go.seq,t,r)}return t??Un(n)}function yg(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function yc(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var yo=gg;var Qn=class{#t={};constructor(){this.register("uuid",new wo),this.register("cuid",new Eo),this.register("ulid",new xo),this.register("nanoid",new bo),this.register("product",new Po)}snapshot(){return Object.create(this.#t,{now:{value:new ho}})}register(t,r){this.#t[t]=r}},ho=class{#t=new Date;generate(){return this.#t.toISOString()}},wo=class{generate(t){if(t===4)return fo();if(t===7)return yo();throw new Error("Invalid UUID generator arguments")}},Eo=class{generate(t){if(t===1)return El();if(t===2)return(0,hc.createId)();throw new Error("Invalid CUID generator arguments")}},xo=class{generate(){return gc()}},bo=class{generate(t){if(typeof t=="number")return po(t);if(t===void 0)return po();throw new Error("Invalid Nanoid generator arguments")}},Po=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};function Hn(e,t){return e==null?e:typeof e=="string"?Hn(JSON.parse(e),t):Array.isArray(e)?wg(e,t):hg(e,t)}function hg(e,t){if(t.pagination){let{skip:r,take:n,cursor:i}=t.pagination;if(r!==null&&r>0||n===0||i!==null&&!Rt(e,i))return null}return Ec(e,t.nested)}function Ec(e,t){for(let[r,n]of Object.entries(t))e[r]=Hn(e[r],n);return e}function wg(e,t){if(t.distinct!==null){let r=t.linkingFields!==null?[...t.distinct,...t.linkingFields]:t.distinct;e=Eg(e,r)}return t.pagination&&(e=xg(e,t.pagination,t.linkingFields)),t.reverse&&e.reverse(),Object.keys(t.nested).length===0?e:e.map(r=>Ec(r,t.nested))}function Eg(e,t){let r=new Set,n=[];for(let i of e){let o=Rr(i,t);r.has(o)||(r.add(o),n.push(i))}return n}function xg(e,t,r){if(r===null)return wc(e,t);let n=new Map;for(let o of e){let s=Rr(o,r);n.has(s)||n.set(s,[]),n.get(s).push(o)}let i=Array.from(n.entries());return i.sort(([o],[s])=>o<s?-1:o>s?1:0),i.flatMap(([,o])=>wc(o,t))}function wc(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>Rt(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function Rr(e,t){return JSON.stringify(t.map(r=>e[r]))}function vo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function To(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function Ro(e,t,r,n){let i=e.args.map(o=>we(o,t,r));switch(e.type){case"rawSql":return[vg(e.sql,i,e.argTypes)];case"templateSql":return(e.chunkable?Ag(e.fragments,i,n):[i]).map(s=>{if(n!==void 0&&s.length>n)throw new re("The query parameter limit supported by your database is exceeded.","P2029");return bg(e.fragments,e.placeholderFormat,s,e.argTypes)});default:O(e.type,"Invalid query type")}}function we(e,t,r){for(;Tg(e);)if(vo(e)){let n=t[e.prisma__value.name];if(n===void 0)throw new Error(`Missing value for query variable ${e.prisma__value.name}`);e=n}else if(To(e)){let{name:n,args:i}=e.prisma__value,o=r[n];if(!o)throw new Error(`Encountered an unknown generator '${n}'`);e=o.generate(...i.map(s=>we(s,t,r)))}else O(e,`Unexpected unevaluated value type: ${e}`);return Array.isArray(e)&&(e=e.map(n=>we(n,t,r))),e}function bg(e,t,r,n){let i="",o={placeholderNumber:1},s=[],a=[];for(let l of Co(e,r,n)){if(i+=Pg(l,t,o),l.type==="stringChunk")continue;let c=s.length,u=s.push(...xc(l))-c;if(l.argType.arity==="tuple"){if(u%l.argType.elements.length!==0)throw new Error(`Malformed query template. Expected the number of parameters to match the tuple arity, but got ${u} parameters for a tuple of arity ${l.argType.elements.length}.`);for(let p=0;p<u/l.argType.elements.length;p++)a.push(...l.argType.elements)}else for(let p=0;p<u;p++)a.push(l.argType)}return{sql:i,args:s,argTypes:a}}function Pg(e,t,r){let n=e.type;switch(n){case"parameter":return Ao(t,r.placeholderNumber++);case"stringChunk":return e.chunk;case"parameterTuple":return`(${e.value.length==0?"NULL":e.value.map(()=>Ao(t,r.placeholderNumber++)).join(",")})`;case"parameterTupleList":return e.value.map(i=>{let o=i.map(()=>Ao(t,r.placeholderNumber++)).join(e.itemSeparator);return`${e.itemPrefix}${o}${e.itemSuffix}`}).join(e.groupSeparator);default:O(n,"Invalid fragment type")}}function Ao(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function vg(e,t,r){return{sql:e,args:t,argTypes:r}}function Tg(e){return vo(e)||To(e)}function*Co(e,t,r){let n=0;for(let i of e)switch(i.type){case"parameter":{if(n>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);yield{...i,value:t[n],argType:r?.[n]},n++;break}case"stringChunk":{yield i;break}case"parameterTuple":{if(n>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);let o=t[n];yield{...i,value:Array.isArray(o)?o:[o],argType:r?.[n]},n++;break}case"parameterTupleList":{if(n>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);let o=t[n];if(!Array.isArray(o))throw new Error("Malformed query template. Tuple list expected.");if(o.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");for(let s of o)if(!Array.isArray(s))throw new Error("Malformed query template. Tuple expected.");yield{...i,value:o,argType:r?.[n]},n++;break}}}function*xc(e){switch(e.type){case"parameter":yield e.value;break;case"stringChunk":break;case"parameterTuple":yield*e.value;break;case"parameterTupleList":for(let t of e.value)yield*t;break}}function Ag(e,t,r){let n=0,i=0;for(let s of Co(e,t,void 0)){let a=0;for(let l of xc(s))a++;i=Math.max(i,a),n+=a}let o=[[]];for(let s of Co(e,t,void 0))switch(s.type){case"parameter":{for(let a of o)a.push(s.value);break}case"stringChunk":break;case"parameterTuple":{let a=s.value.length,l=[];if(r&&o.length===1&&a===i&&n>r&&n-a<r){let c=r-(n-a);l=Cg(s.value,c)}else l=[s.value];o=o.flatMap(c=>l.map(u=>[...c,u]));break}case"parameterTupleList":{let a=s.value.reduce((p,d)=>p+d.length,0),l=[],c=[],u=0;for(let p of s.value)r&&o.length===1&&a===i&&c.length>0&&n-a+u+p.length>r&&(l.push(c),c=[],u=0),c.push(p),u+=p.length;c.length>0&&l.push(c),o=o.flatMap(p=>l.map(d=>[...p,d]));break}}return o}function Cg(e,t){let r=[];for(let n=0;n<e.length;n+=t)r.push(e.slice(n,n+t));return r}function bc(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let a=0;a<o.length;a++){let l=o[a];a===o.length-1?s[l]=n:(s[l]===void 0&&(s[l]={}),s=s[l])}return r},{}))}function Pc(e){return{columns:e.columnNames,types:e.columnTypes.map(t=>Rg(t)),rows:e.rows.map(t=>t.map((r,n)=>Nt(r,e.columnTypes[n])))}}function Nt(e,t){if(e===null)return null;switch(t){case x.Int32:switch(typeof e){case"number":return Math.trunc(e);case"string":return Math.trunc(Number(e));default:throw new Error(`Cannot serialize value of type ${typeof e} as Int32`)}case x.Int32Array:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as Int32Array`);return e.map(r=>Nt(r,x.Int32));case x.Int64:switch(typeof e){case"number":return BigInt(Math.trunc(e));case"string":return e;default:throw new Error(`Cannot serialize value of type ${typeof e} as Int64`)}case x.Int64Array:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as Int64Array`);return e.map(r=>Nt(r,x.Int64));case x.Json:switch(typeof e){case"string":return JSON.parse(e);default:throw new Error(`Cannot serialize value of type ${typeof e} as Json`)}case x.JsonArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as JsonArray`);return e.map(r=>Nt(r,x.Json));case x.Bytes:if(Array.isArray(e))return new Uint8Array(e);throw new Error(`Cannot serialize value of type ${typeof e} as Bytes`);case x.BytesArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as BytesArray`);return e.map(r=>Nt(r,x.Bytes));case x.Boolean:switch(typeof e){case"boolean":return e;case"string":return e==="true"||e==="1";case"number":return e===1;default:throw new Error(`Cannot serialize value of type ${typeof e} as Boolean`)}case x.BooleanArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as BooleanArray`);return e.map(r=>Nt(r,x.Boolean));default:return e}}function Rg(e){switch(e){case x.Int32:return"int";case x.Int64:return"bigint";case x.Float:return"float";case x.Double:return"double";case x.Text:return"string";case x.Enum:return"enum";case x.Bytes:return"bytes";case x.Boolean:return"bool";case x.Character:return"char";case x.Numeric:return"decimal";case x.Json:return"json";case x.Uuid:return"uuid";case x.DateTime:return"datetime";case x.Date:return"date";case x.Time:return"time";case x.Int32Array:return"int-array";case x.Int64Array:return"bigint-array";case x.FloatArray:return"float-array";case x.DoubleArray:return"double-array";case x.TextArray:return"string-array";case x.EnumArray:return"string-array";case x.BytesArray:return"bytes-array";case x.BooleanArray:return"bool-array";case x.CharacterArray:return"char-array";case x.NumericArray:return"decimal-array";case x.JsonArray:return"json-array";case x.UuidArray:return"uuid-array";case x.DateTimeArray:return"datetime-array";case x.DateArray:return"date-array";case x.TimeArray:return"time-array";case x.UnknownNumber:return"unknown";case x.Set:return"string";default:O(e,`Unexpected column type: ${e}`)}}function vc(e,t,r){if(!t.every(n=>So(e,n))){let n=Sg(e,r),i=Ig(r);throw new re(n,i,r.context)}}function So(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:O(t,`Unknown rule type: ${t.type}`)}}function Sg(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:O(t,`Unknown error identifier: ${t}`)}}function Ig(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:O(e,`Unknown error identifier: ${e}`)}}var Sr=class e{#t;#e;#r;#n=new Qn;#s;#i;#a;#o;#c;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s,provider:a,connectionInfo:l}){this.#t=t,this.#e=r,this.#r=n,this.#s=i,this.#i=o,this.#a=s??o,this.#o=a,this.#c=l}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:bc,rawSerializer:Pc,provider:t.provider,connectionInfo:t.connectionInfo})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#e,this.#n.snapshot()).catch(i=>St(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:we(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!Tc(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(Io(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>Ae(s)+Ae(a)):0}}case"execute":{let o=Ro(t.args,n,i,this.#l()),s=0;for(let a of o)s+=await this.#u(a,r,()=>r.executeRaw(a).catch(l=>t.args.type==="rawSql"?Zi(l):St(l)));return{value:s}}case"query":{let o=Ro(t.args,n,i,this.#l()),s;for(let a of o){let l=await this.#u(a,r,()=>r.queryRaw(a).catch(c=>t.args.type==="rawSql"?Zi(c):St(c)));s===void 0?s=l:(s.rows.push(...l.rows),s.lastInsertId=l.lastInsertId)}return{value:t.args.type==="rawSql"?this.#a(s):this.#i(s),lastInsertId:s?.lastInsertId}}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(Tc(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:Ac(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async l=>({joinExpr:l,childRecords:(await this.interpretNode(l.child,r,n,i)).value})));return{value:kg(o,a),lastInsertId:s}}case"transaction":{if(!this.#t.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#t.manager,s=await o.startTransaction(),a=await o.getTransaction(s,"query");try{let l=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),l}catch(l){throw await o.rollbackTransaction(s.id),l}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:yl(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return vc(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return So(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(Io(s).map(l=>JSON.stringify(l)));return{value:Io(o).filter(l=>!a.has(JSON.stringify(l)))}}case"process":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Hn(o,t.args.operations),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,l]of Object.entries(t.args.fields))s[a]=Og(l,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:ko(o);for(let[l,c]of Object.entries(t.args.fields))a[l]=Dg(c,a[l],n,i);return{value:a,lastInsertId:s}}default:O(t,`Unexpected node type: ${t.type}`)}}#l(){return this.#c?.maxBindValues!==void 0?this.#c.maxBindValues:this.#p()}#p(){if(this.#o!==void 0)switch(this.#o){case"cockroachdb":case"postgres":case"postgresql":case"prisma+postgres":return 32766;case"mysql":return 65535;case"sqlite":return 999;case"sqlserver":return 2098;case"mongodb":return;default:O(this.#o,`Unexpected provider: ${this.#o}`)}}#u(t,r,n){return Mn({query:t,execute:n,provider:this.#o??r.provider,tracingHelper:this.#s,onQuery:this.#r})}};function Tc(e){return Array.isArray(e)?e.length===0:e==null}function Io(e){return Array.isArray(e)?e:[e]}function Ae(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function ko(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function Ac(e,t){return Array.isArray(e)?e.map(r=>Ac(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function kg(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let l=ko(a),c=Rr(l,i);s[c]||(s[c]=[]),s[c].push(l),r.isRelationUnique?l[r.parentField]=null:l[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let l=Rr(ko(a),o);for(let c of s[l]??[])r.isRelationUnique?c[r.parentField]=a:c[r.parentField].push(a)}}return e}function Og(e,t,r,n){switch(e.type){case"value":return we(e.value,r,n);case"lastInsertId":return t;default:O(e,`Unexpected field initializer type: ${e.type}`)}}function Dg(e,t,r,n){switch(e.type){case"set":return we(e.value,r,n);case"add":return Ae(t)+Ae(we(e.value,r,n));case"subtract":return Ae(t)-Ae(we(e.value,r,n));case"multiply":return Ae(t)*Ae(we(e.value,r,n));case"divide":{let i=Ae(t),o=Ae(we(e.value,r,n));return o===0?null:i/o}default:O(e,`Unexpected field operation type: ${e.type}`)}}async function Ng(){return globalThis.crypto??await import("node:crypto")}async function Cc(){return(await Ng()).randomUUID()}var de=class extends re{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Ir=class extends de{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Gn=class extends de{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Jn=class extends de{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Wn=class extends de{constructor(){super("Unable to start a transaction in the given time.")}},Kn=class extends de{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction.`,{operation:t,timeout:r,timeTaken:n})}},_t=class extends de{constructor(t){super(`Internal Consistency Error: ${t}`)}},zn=class extends de{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var _g=100,kr=q("prisma:client:transactionManager"),Mg=()=>({sql:"COMMIT",args:[],argTypes:[]}),Fg=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),Lg=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),$g=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),Or=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#t;#e;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i,provider:o}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#t=i,this.#e=o}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.#a(t):this.transactionOptions,n={id:await Cc(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n);let i=!1,o=setTimeout(()=>i=!0,r.maxWait);switch(n.transaction=await this.driverAdapter.startTransaction(r.isolationLevel).catch(St),clearTimeout(o),n.status){case"waiting":if(i)throw await this.#i(n,"timed_out"),new Wn;return n.status="running",n.timer=this.#s(n.id,r.timeout),{id:n.id};case"timed_out":case"running":case"committed":case"rolled_back":throw new _t(`Transaction in invalid state ${n.status} although it just finished startup.`);default:O(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.#n(t,"commit");await this.#i(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.#n(t,"rollback");await this.#i(r,"rolled_back")})}async getTransaction(t,r){let n=this.#n(t.id,r);if(n.status==="closing"&&(await n.closing,n=this.#n(t.id,r)),!n.transaction)throw new Ir;return n.transaction}#n(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(kr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"closing":case"waiting":case"running":throw new _t("Active transaction found in closed transactions list.");case"committed":throw new Gn(r);case"rolled_back":throw new Jn(r);case"timed_out":throw new Kn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw kr("Transaction not found.",t),new Ir}if(["committed","rolled_back","timed_out"].includes(n.status))throw new _t("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.#i(t,"rolled_back")))}#s(t,r){let n=Date.now();return setTimeout(async()=>{kr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.#i(i,"timed_out"):kr("Transaction already committed or rolled back when timeout happened.",t)},r)}async#i(t,r){let n=async()=>{kr("Closing transaction.",{transactionId:t.id,status:r});try{if(t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#o(Lg(),t.transaction,()=>t.transaction.commit());else{let i=Mg();await this.#o(i,t.transaction,()=>t.transaction.executeRaw(i)),await t.transaction.commit()}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#o($g(),t.transaction,()=>t.transaction.rollback());else{let i=Fg();await this.#o(i,t.transaction,()=>t.transaction.executeRaw(i)),await t.transaction.rollback()}}finally{t.status=r,clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>_g&&this.closedTransactions.shift()}};t.status==="closing"?(await t.closing,this.#n(t.id,r==="committed"?"commit":"rollback")):await Object.assign(t,{status:"closing",reason:r,closing:n()}).closing}#a(t){if(!t.timeout)throw new de("timeout is required");if(!t.maxWait)throw new de("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new zn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#o(t,r,n){return Mn({query:t,execute:n,provider:this.#e??r.provider,tracingHelper:this.tracingHelper,onQuery:this.#t})}};var Yn="6.15.0";var Zn=class e{#t;#e;#r;#n;constructor(t,r,n){this.#t=t,this.#e=r,this.#r=n,this.#n=r.getConnectionInfo?.()}static async connect(t){let r,n;try{r=await t.driverAdapterFactory.connect(),n=new Or({driverAdapter:r,transactionOptions:t.transactionOptions,tracingHelper:t.tracingHelper,onQuery:t.onQuery,provider:t.provider})}catch(i){throw await r?.dispose(),i}return new e(t,r,n)}getConnectionInfo(){let t=this.#n??{supportsRelationJoins:!1};return Promise.resolve({provider:this.#e.provider,connectionInfo:t})}async execute({plan:t,placeholderValues:r,transaction:n,batchIndex:i}){let o=n?await this.#r.getTransaction(n,i!==void 0?"batch query":"query"):this.#e;return await Sr.forSql({transactionManager:n?{enabled:!1}:{enabled:!0,manager:this.#r},placeholderValues:r,onQuery:this.#t.onQuery,tracingHelper:this.#t.tracingHelper,provider:this.#t.provider,connectionInfo:this.#n}).run(t,o)}async startTransaction(t){return{...await this.#r.startTransaction(t),payload:void 0}}async commitTransaction(t){await this.#r.commitTransaction(t.id)}async rollbackTransaction(t){await this.#r.rollbackTransaction(t.id)}async disconnect(){try{await this.#r.cancelAllTransactions()}finally{await this.#e.dispose()}}};var Xn=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function Rc(e,t,r){let n=r||{},i=n.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!Xn.test(e))throw new TypeError("argument name is invalid");let o=i(t);if(o&&!Xn.test(o))throw new TypeError("argument val is invalid");let s=e+"="+o;if(n.maxAge!==void 0&&n.maxAge!==null){let a=n.maxAge-0;if(Number.isNaN(a)||!Number.isFinite(a))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(a)}if(n.domain){if(!Xn.test(n.domain))throw new TypeError("option domain is invalid");s+="; Domain="+n.domain}if(n.path){if(!Xn.test(n.path))throw new TypeError("option path is invalid");s+="; Path="+n.path}if(n.expires){if(!qg(n.expires)||Number.isNaN(n.expires.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():n.priority){case"low":{s+="; Priority=Low";break}case"medium":{s+="; Priority=Medium";break}case"high":{s+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:{s+="; SameSite=Strict";break}case"lax":{s+="; SameSite=Lax";break}case"strict":{s+="; SameSite=Strict";break}case"none":{s+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return n.partitioned&&(s+="; Partitioned"),s}function qg(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function Sc(e,t){let r=(e||"").split(";").filter(l=>typeof l=="string"&&!!l.trim()),n=r.shift()||"",i=Ug(n),o=i.name,s=i.value;try{s=t?.decode===!1?s:(t?.decode||decodeURIComponent)(s)}catch{}let a={name:o,value:s};for(let l of r){let c=l.split("="),u=(c.shift()||"").trimStart().toLowerCase(),p=c.join("=");switch(u){case"expires":{a.expires=new Date(p);break}case"max-age":{a.maxAge=Number.parseInt(p,10);break}case"secure":{a.secure=!0;break}case"httponly":{a.httpOnly=!0;break}case"samesite":{a.sameSite=p;break}default:a[u]=p}}return a}function Ug(e){let t="",r="",n=e.split("=");return n.length>1?(t=n.shift(),r=n.join("=")):r=e,{name:t,value:r}}function Mt({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new _(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new _("error: Missing URL environment variable, value, or override.",n);return i}var ei=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var le=class extends ei{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};function R(e,t){return{...e,isRetryable:t}}var it=class extends le{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,R(r,!1))}};P(it,"InvalidDatasourceError");function ti(e){let t={clientVersion:e.clientVersion},r=Object.keys(e.inlineDatasources)[0],n=Mt({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,clientVersion:e.clientVersion,env:{...e.env,...typeof process<"u"?process.env:{}}}),i;try{i=new URL(n)}catch{throw new it(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==Yr)throw new it(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new it(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);let l=fi(i)?"http:":"https:",c=new URL(i.href.replace(o,l));return{apiKey:a,url:c}}var Ic=fe(us()),Ft=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,transactionId:r}={}){let n={Accept:"application/json",Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","Prisma-Engine-Hash":this.engineHash,"Prisma-Engine-Version":Ic.enginesVersion};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-Transaction-Id"]=r);let i=this.#t();return i.length>0&&(n["X-Capture-Telemetry"]=i.join(", ")),n}#t(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}};function jg(e){return e[0]*1e3+e[1]/1e6}function Lt(e){return new Date(jg(e))}var kc=q("prisma:client:clientEngine:remoteExecutor"),ri=class{#t;#e;#r;#n;#s;constructor(t){this.#t=t.clientVersion,this.#n=t.logEmitter,this.#s=t.tracingHelper;let{url:r,apiKey:n}=ti({clientVersion:t.clientVersion,env:t.env,inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources});this.#r=new Oo(r),this.#e=new Ft({apiKey:n,engineHash:t.clientVersion,logLevel:t.logLevel,logQueries:t.logQueries,tracingHelper:t.tracingHelper})}async getConnectionInfo(){return await this.#i({path:"/connection-info",method:"GET"})}async execute({plan:t,placeholderValues:r,batchIndex:n,model:i,operation:o,transaction:s,customFetch:a}){return(await this.#i({path:s?`/transaction/${s.id}/query`:"/query",method:"POST",body:{model:i,operation:o,plan:t,params:r},batchRequestIdx:n,fetch:a})).data}async startTransaction(t){return{...await this.#i({path:"/transaction/start",method:"POST",body:t}),payload:void 0}}async commitTransaction(t){await this.#i({path:`/transaction/${t.id}/commit`,method:"POST"})}async rollbackTransaction(t){await this.#i({path:`/transaction/${t.id}/rollback`,method:"POST"})}disconnect(){return Promise.resolve()}async#i({path:t,method:r,body:n,fetch:i=globalThis.fetch,batchRequestIdx:o}){let s=await this.#r.request({method:r,path:t,headers:this.#e.build(),body:n,fetch:i});s.ok||await this.#a(s,o);let a=await s.json();return typeof a.extensions=="object"&&a.extensions!==null&&this.#o(a.extensions),a}async#a(t,r){let n=t.headers.get("Prisma-Error-Code"),i=await t.text(),o,s=i;try{o=JSON.parse(i)}catch{o={}}typeof o.code=="string"&&(n=o.code),typeof o.error=="string"?s=o.error:typeof o.message=="string"?s=o.message:typeof o.InvalidRequestError=="object"&&o.InvalidRequestError!==null&&typeof o.InvalidRequestError.reason=="string"&&(s=o.InvalidRequestError.reason),s=s||`HTTP ${t.status}: ${t.statusText}`;let a=typeof o.meta=="object"&&o.meta!==null?o.meta:o;throw new B(s,{clientVersion:this.#t,code:n??"P6000",batchRequestIdx:r,meta:a})}#o(t){if(t.logs)for(let r of t.logs)this.#c(r);t.traces&&this.#s.dispatchEngineSpans(t.traces)}#c(t){switch(t.level){case"debug":case"trace":kc(t);break;case"error":case"warn":case"info":{this.#n.emit(t.level,{timestamp:Lt(t.timestamp),message:t.attributes.message??"",target:t.target});break}case"query":{this.#n.emit("query",{query:t.attributes.query??"",timestamp:Lt(t.timestamp),duration:t.attributes.duration_ms??0,params:t.attributes.params??"",target:t.target});break}default:throw new Error(`Unexpected log level: ${t.level}`)}}},Oo=class{#t;#e;#r;constructor(t){this.#t=t,this.#e=new Map}async request({method:t,path:r,headers:n,body:i,fetch:o}){let s=new URL(r,this.#t),a=this.#n(s);a&&(n.Cookie=a),this.#r&&(n["Accelerate-Query-Engine-Jwt"]=this.#r);let l=await o(s.href,{method:t,body:i!==void 0?JSON.stringify(i):void 0,headers:n});return kc(t,s,l.status,l.statusText),this.#r=l.headers.get("Accelerate-Query-Engine-Jwt")??void 0,this.#s(s,l),l}#n(t){let r=[],n=new Date;for(let[i,o]of this.#e){if(o.expires&&o.expires<n){this.#e.delete(i);continue}let s=o.domain??t.hostname,a=o.path??"/";t.hostname.endsWith(s)&&t.pathname.startsWith(a)&&r.push(Rc(o.name,o.value))}return r.length>0?r.join("; "):void 0}#s(t,r){let n=r.headers.getSetCookie?.()||[];if(n.length===0){let i=r.headers.get("Set-Cookie");i&&n.push(i)}for(let i of n){let o=Sc(i),s=o.domain??t.hostname,a=o.path??"/",l=`${s}:${a}:${o.name}`;this.#e.set(l,{name:o.name,value:o.value,domain:s,path:a,expires:o.expires})}}};var Do,Oc={async loadQueryCompiler(e){let{clientVersion:t,compilerWasm:r}=e;if(r===void 0)throw new _("WASM query compiler was unexpectedly `undefined`",t);return Do===void 0&&(Do=(async()=>{let n=await r.getRuntime(),i=await r.getQueryCompilerWasmModule();if(i==null)throw new _("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let o={"./query_compiler_bg.js":n},s=new WebAssembly.Instance(i,o),a=s.exports.__wbindgen_start;return n.__wbg_set_wasm(s.exports),a(),n.QueryCompiler})()),await Do}};var Dc="P2038",Dr=q("prisma:client:clientEngine"),_c=globalThis;_c.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new oe(e,Yn)}};var $t=class{name="ClientEngine";#t;#e={type:"disconnected"};#r;#n;config;datamodel;logEmitter;logQueries;logLevel;tracingHelper;#s;constructor(t,r,n){if(!t.previewFeatures?.includes("driverAdapters")&&!r)throw new _("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,Dc);if(r)this.#n={remote:!0};else if(t.adapter)this.#n={remote:!1,driverAdapterFactory:t.adapter},Dr("Using driver adapter: %O",t.adapter);else throw new _("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,Dc);this.#r=n??Oc,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#s=i=>{this.logEmitter.emit("query",{...i,params:Er(i.params),target:"ClientEngine"})})}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async#i(){switch(this.#e.type){case"disconnected":{let t=this.tracingHelper.runInChildSpan("connect",async()=>{let r,n;try{r=await this.#a(),n=await this.#o(r)}catch(o){throw this.#e={type:"disconnected"},n?.free(),await r?.disconnect(),o}let i={executor:r,queryCompiler:n};return this.#e={type:"connected",engine:i},i});return this.#e={type:"connecting",promise:t},await t}case"connecting":return await this.#e.promise;case"connected":return this.#e.engine;case"disconnecting":return await this.#e.promise,await this.#i()}}async#a(){return this.#n.remote?new ri({clientVersion:this.config.clientVersion,env:this.config.env,inlineDatasources:this.config.inlineDatasources,logEmitter:this.logEmitter,logLevel:this.logLevel,logQueries:this.logQueries,overrideDatasources:this.config.overrideDatasources,tracingHelper:this.tracingHelper}):await Zn.connect({driverAdapterFactory:this.#n.driverAdapterFactory,tracingHelper:this.tracingHelper,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#d(this.config.transactionOptions.isolationLevel)},onQuery:this.#s,provider:this.config.activeProvider})}async#o(t){let r=this.#t;r===void 0&&(r=await this.#r.loadQueryCompiler(this.config),this.#t=r);let{provider:n,connectionInfo:i}=await t.getConnectionInfo();try{return this.#u(()=>new r({datamodel:this.datamodel,provider:n,connectionInfo:i}),void 0,!1)}catch(o){throw this.#c(o)}}#c(t){if(t instanceof oe)return t;try{let r=JSON.parse(t.message);return new _(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#l(t,r){if(t instanceof _)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new oe(Nc(this,t.message,r),this.config.clientVersion);if(t instanceof re)return new B(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let n=JSON.parse(t);return new Y(`${n.message}
${n.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#p(t){return t instanceof oe?t:typeof t.message=="string"&&typeof t.code=="string"?new B(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):typeof t.message=="string"?new Y(t.message,{clientVersion:this.config.clientVersion}):t}#u(t,r,n=!0){let i=_c.PRISMA_WASM_PANIC_REGISTRY.set_message,o;global.PRISMA_WASM_PANIC_REGISTRY.set_message=s=>{o=s};try{return t()}finally{if(global.PRISMA_WASM_PANIC_REGISTRY.set_message=i,o)throw this.#t=void 0,n&&this.stop().catch(s=>Dr("failed to disconnect:",s)),new oe(Nc(this,o,r),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.#i()}async stop(){switch(this.#e.type){case"disconnected":return;case"connecting":return await this.#e.promise,await this.stop();case"connected":{let t=this.#e.engine,r=this.tracingHelper.runInChildSpan("disconnect",async()=>{try{await t.executor.disconnect(),t.queryCompiler.free()}finally{this.#e={type:"disconnected"}}});return this.#e={type:"disconnecting",promise:r},await r}case"disconnecting":return await this.#e.promise}}version(){return"unknown"}async transaction(t,r,n){let i,{executor:o}=await this.#i();try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#d(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s)}else Ie(t,"Invalid transaction action.")}catch(s){throw this.#l(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{interactiveTransaction:r,customDataProxyFetch:n}){Dr("sending request");let i=JSON.stringify(t),{executor:o,queryCompiler:s}=await this.#i().catch(l=>{throw this.#l(l,i)}),a;try{a=this.#u(()=>s.compile(i),i)}catch(l){throw this.#p(l)}try{Dr("query plan created",a);let l={},c=await o.execute({plan:a,model:t.modelName,operation:t.action,placeholderValues:l,transaction:r,batchIndex:void 0,customFetch:n?.(globalThis.fetch)});return Dr("query plan executed"),{data:{[t.action]:c}}}catch(l){throw this.#l(l,i)}}async requestBatch(t,{transaction:r,customDataProxyFetch:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(At(t,r)),{executor:s,queryCompiler:a}=await this.#i().catch(c=>{throw this.#l(c,o)}),l;try{l=a.compileBatch(o)}catch(c){throw this.#p(c)}try{let c;r?.kind==="itx"&&(c=r.options);let u={};switch(l.type){case"multi":{if(r?.kind!=="itx"){let m=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;c=await this.transaction("start",{},m)}let p=[],d=!1;for(let[m,y]of l.plans.entries())try{let g=await s.execute({plan:y,placeholderValues:u,model:t[m].modelName,operation:t[m].action,batchIndex:m,transaction:c,customFetch:n?.(globalThis.fetch)});p.push({data:{[t[m].action]:g}})}catch(g){p.push(g),d=!0;break}return c!==void 0&&r?.kind!=="itx"&&(d?await this.transaction("rollback",{},c):await this.transaction("commit",{},c)),p}case"compacted":{if(!t.every(m=>m.action===i))throw new Error("All queries in a batch must have the same action");let p=await s.execute({plan:l.plan,placeholderValues:u,model:t[0].modelName,operation:i,batchIndex:void 0,transaction:c,customFetch:n?.(globalThis.fetch)});return fl(p,l).map(m=>({data:{[i]:m}}))}}}catch(c){throw this.#l(c,o)}}metrics(t){throw new Error("Method not implemented.")}#d(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new B(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function Nc(e,t,r){return al({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:r})}var Vt=class extends le{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",R(t,!0))}};P(Vt,"ForcedRetryError");var ot=class extends le{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,R(r,!1))}};P(ot,"NotImplementedYetError");var L=class extends le{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var st=class extends L{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",R(t,!0))}};P(st,"SchemaMissingError");var No="This request could not be understood by the server",Nr=class extends L{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||No,R(t,!1)),n&&(this.code=n)}};P(Nr,"BadRequestError");var _r=class extends L{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",R(t,!0)),this.logs=r}};P(_r,"HealthcheckTimeoutError");var Mr=class extends L{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,R(t,!0)),this.logs=n}};P(Mr,"EngineStartupError");var Fr=class extends L{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",R(t,!1))}};P(Fr,"EngineVersionNotSupportedError");var _o="Request timed out",Lr=class extends L{name="GatewayTimeoutError";code="P5009";constructor(t,r=_o){super(r,R(t,!1))}};P(Lr,"GatewayTimeoutError");var Bg="Interactive transaction error",$r=class extends L{name="InteractiveTransactionError";code="P5015";constructor(t,r=Bg){super(r,R(t,!1))}};P($r,"InteractiveTransactionError");var Qg="Request parameters are invalid",Vr=class extends L{name="InvalidRequestError";code="P5011";constructor(t,r=Qg){super(r,R(t,!1))}};P(Vr,"InvalidRequestError");var Mo="Requested resource does not exist",qr=class extends L{name="NotFoundError";code="P5003";constructor(t,r=Mo){super(r,R(t,!1))}};P(qr,"NotFoundError");var Fo="Unknown server error",qt=class extends L{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||Fo,R(t,!0)),this.logs=n}};P(qt,"ServerError");var Lo="Unauthorized, check your connection string",Ur=class extends L{name="UnauthorizedError";code="P5007";constructor(t,r=Lo){super(r,R(t,!1))}};P(Ur,"UnauthorizedError");var $o="Usage exceeded, retry again later",jr=class extends L{name="UsageExceededError";code="P5008";constructor(t,r=$o){super(r,R(t,!0))}};P(jr,"UsageExceededError");async function Hg(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Br(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await Hg(e);if(n.type==="QueryEngineError")throw new B(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new qt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new st(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Fr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Mr(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new _(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new _r(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new $r(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Vr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Ur(r,Ut(Lo,n));if(e.status===404)return new qr(r,Ut(Mo,n));if(e.status===429)throw new jr(r,Ut($o,n));if(e.status===504)throw new Lr(r,Ut(_o,n));if(e.status>=500)throw new qt(r,Ut(Fo,n));if(e.status>=400)throw new Nr(r,Ut(No,n))}function Ut(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function Mc(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Fc(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,l,c,u;for(let p=0;p<o;p=p+3)u=t[p]<<16|t[p+1]<<8|t[p+2],s=(u&16515072)>>18,a=(u&258048)>>12,l=(u&4032)>>6,c=u&63,r+=Fe[s]+Fe[a]+Fe[l]+Fe[c];return i==1?(u=t[o],s=(u&252)>>2,a=(u&3)<<4,r+=Fe[s]+Fe[a]+"=="):i==2&&(u=t[o]<<8|t[o+1],s=(u&64512)>>10,a=(u&1008)>>4,l=(u&15)<<2,r+=Fe[s]+Fe[a]+Fe[l]+"="),r}function Lc(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new _("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}var $c={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.15.0-5.85179d7826409ee107a6ba334b5e305ae3fba9fb","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var Qr=class extends le{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,R(r,!0))}};P(Qr,"RequestError");async function at(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new Qr(a,{clientVersion:n,cause:s})}}var Jg=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,Vc=q("prisma:client:dataproxyEngine");async function Wg(e,t){let r=$c["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&Jg.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,l,c]=s.split("."),u=Kg(`<=${a}.${l}.${c}`),p=await at(u,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let d=await p.text();Vc("length of body fetched from unpkg.com",d.length);let m;try{m=JSON.parse(d)}catch(y){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),y}return m.version}throw new ot("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function qc(e,t){let r=await Wg(e,t);return Vc("version",r),r}function Kg(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Uc=3,Hr=q("prisma:client:dataproxyEngine"),Gr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){Lc(t),this.config=t,this.env=t.env,this.inlineSchema=Fc(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.protocol=r.protocol,this.headerBuilder=new Ft({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel??"error",logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await qc(this.host,this.config),Hr("host",this.host),Hr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Hr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Lt(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Lt(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await at(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Hr("schema response status",r.status);let n=await Br(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=At(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(l=>(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l?this.convertProtocolErrorsToClientError(l.errors):l))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await at(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,transactionId:i?.id}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||Hr("graphql response status",a.status),await this.handleError(await Br(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await at(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Br(l,this.clientVersion));let c=await l.json(),{extensions:u}=c;u&&this.propagateResponseExtensions(u);let p=c.id,d=c["data-proxy"].endpoint;return{id:p,payload:{endpoint:d}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await at(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Br(a,this.clientVersion));let l=await a.json(),{extensions:c}=l;c&&this.propagateResponseExtensions(c);return}}})}getURLAndAPIKey(){return ti({clientVersion:this.clientVersion,env:this.env,inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources})}metrics(){throw new ot("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof le)||!i.isRetryable)throw i;if(r>=Uc)throw i instanceof Vt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Uc} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Mc(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof st)throw await this.uploadSchema(),new Vt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Dn(t[0],this.config.clientVersion,this.config.activeProvider):new Y(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function jc({url:e,adapter:t,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=g=>{i.push({_tag:"warning",value:g})},a=g=>{let I=g.join(`
`);o.push({_tag:"error",value:I})},l=!!e?.startsWith("prisma://"),c=Zr(e),u=!!t,p=l||c;!u&&r&&p&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let d=p||!r;u&&(d||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):r?l&&a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]):a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let m={accelerate:d,ppg:c,driverAdapters:u};function y(g){return g.length>0}return y(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:m}:{ok:!0,diagnostics:{warnings:i},isUsing:m}}function Bc({copyEngine:e=!0},t){let r;try{r=Mt({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=jc({url:r,adapter:t.adapter,copyEngine:e,targetBuildType:"client"});for(let p of o.warnings)rn(...p.value);if(!n){let p=o.errors[0];throw new ee(p.value,{clientVersion:t.clientVersion})}let s=ut(t.generator),a=s==="library",l=s==="binary",c=s==="client",u=(i.accelerate||i.ppg)&&!i.driverAdapters;return c?new $t(t,u):i.accelerate?new Gr(t):(i.driverAdapters,i.accelerate,new $t(t,u))}function ni({generator:e}){return e?.previewFeatures??[]}var Qc=e=>({command:e});var Hc=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function jt(e){try{return Gc(e,"fast")}catch{return Gc(e,"slow")}}function Gc(e,t){return JSON.stringify(e.map(r=>Wc(r,t)))}function Wc(e,t){if(Array.isArray(e))return e.map(r=>Wc(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(pt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(ae.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(Buffer.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(zg(e))return{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:Buffer.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Kc(e):e}function zg(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Kc(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Jc);let t={};for(let r of Object.keys(e))t[r]=Jc(e[r]);return t}function Jc(e){return typeof e=="bigint"?e.toString():Kc(e)}var Yg=/^(\s*alter\s)/i,zc=q("prisma:client");function Vo(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&Yg.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var qo=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(In(r))n=r.sql,i={values:jt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:jt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:jt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:jt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Hc(r),i={values:jt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?zc(`prisma.${e}(${n}, ${i.values})`):zc(`prisma.${e}(${n})`),{query:n,parameters:i}},Yc={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new pe(t,r)}},Zc={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function Uo(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=Xc(r(s)):Xc(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function Xc(e){return typeof e.then=="function"?e:Promise.resolve(e)}var Zg=pi.split(".")[0],Xg={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},jo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${Zg}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??Xg}};function eu(){return new jo}function tu(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function ru(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var iu=fe(yi());function ii(e){return typeof e.batchRequestIdx=="number"}function nu(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(Bo(e.query.arguments)),t.push(Bo(e.query.selection)),t.join("")}function Bo(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${Bo(n)})`:r}).join(" ")})`}var ey={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Qo(e){return ey[e]}var oi=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function lt(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new ae(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>lt("bigint",r));case"bytes-array":return t.map(r=>lt("bytes",r));case"decimal-array":return t.map(r=>lt("decimal",r));case"datetime-array":return t.map(r=>lt("datetime",r));case"date-array":return t.map(r=>lt("date",r));case"time-array":return t.map(r=>lt("time",r));default:return t}}function Ho(e){let t=[],r=ty(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=lt(e.types[s],i[s]);t.push(o)}return t}function ty(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var ry=q("prisma:client:request_handler"),si=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new oi({batchLoader:za(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),c=n.some(p=>Qo(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:ny(o),containsWrite:c,customDataProxyFetch:i})).map((p,d)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[d],p)}catch(m){return m}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?ou(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Qo(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:nu(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return process.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(ry(t),iy(t,i))throw t;if(t instanceof B&&oy(t)){let c=su(t.meta);An({args:o,errors:[c],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let l=t.message;if(n&&(l=yn({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:l})),l=this.sanitizeMessage(l),t.code){let c=s?{modelName:s,...t.meta}:t.meta;throw new B(l,{code:t.code,clientVersion:this.client._clientVersion,meta:c,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new oe(l,this.client._clientVersion);if(t instanceof Y)throw new Y(l,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof _)throw new _(l,this.client._clientVersion);if(t instanceof oe)throw new oe(l,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,iu.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(c=>c!=="select"&&c!=="include"),a=Hi(o,s),l=i==="queryRaw"?Ho(a):Ze(a);return n?n(l):l}get[Symbol.toStringTag](){return"RequestHandler"}};function ny(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:ou(e)};Ie(e,"Unknown transaction kind")}}function ou(e){return{id:e.id,payload:e.payload}}function iy(e,t){return ii(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function oy(e){return e.code==="P2009"||e.code==="P2012"}function su(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(su)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}var au=Yn;var du=fe(Oi());var D=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};P(D,"PrismaClientConstructorValidationError");var lu=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],cu=["pretty","colorless","minimal"],uu=["info","query","warn","error"],sy={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new D(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Bt(r,t)||` Available datasources: ${t.join(", ")}`;throw new D(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new D(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new D(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new D(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&ut(t.generator)==="client")throw new D('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new D('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!ni(t).includes("driverAdapters"))throw new D('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(ut(t.generator)==="binary")throw new D('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new D(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new D(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!cu.includes(e)){let t=Bt(e,cu);throw new D(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new D(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!uu.includes(r)){let n=Bt(r,uu);throw new D(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Bt(i,o);throw new D(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new D(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new D(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new D(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new D('"omit" option is expected to be an object.');if(e===null)throw new D('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=ly(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let l=o.fields.find(c=>c.name===s);if(!l){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(l.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new D(cy(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new D(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Bt(r,t);throw new D(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function mu(e,t){for(let[r,n]of Object.entries(e)){if(!lu.includes(r)){let i=Bt(r,lu);throw new D(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}sy[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new D('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Bt(e,t){if(t.length===0||typeof e!="string")return"";let r=ay(e,t);return r?` Did you mean "${r}"?`:""}function ay(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,du.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function ly(e,t){return pu(t.models,e)??pu(t.types,e)}function pu(e,t){let r=Object.keys(e).find(n=>Le(n)===t);if(r)return e[r]}function cy(e,t){let r=Pt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=Tn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}function fu(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=c=>{o||(o=!0,r(c))};for(let c=0;c<e.length;c++)e[c].then(u=>{n[c]=u,a()},u=>{if(!ii(u)){l(u);return}u.batchRequestIdx===c?l(u):(i||(i=u),a())})})}var Je=q("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var my={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},fy=Symbol.for("prisma.client.transaction.id"),gy={id:0,nextId(){return++this.id}};function yy(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=Uo();constructor(n){e=n?.__internal?.configOverride?.(e)??e,tl(e),n&&mu(n,e);let i=new py().on("error",()=>{});this._extensions=vt.empty(),this._previewFeatures=ni(e),this._clientVersion=e.clientVersion??au,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=eu();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&Go.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&Go.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let l=e.activeProvider==="postgresql"||e.activeProvider==="cockroachdb"?"postgres":e.activeProvider;if(s.provider!==l)throw new _(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new _("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=!s&&o&&tr(o,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},c=l.__internal??{},u=c.debug===!0;u&&q.enable("prisma:client");let p=Go.resolve(e.dirname,e.relativePath);dy.existsSync(p)||(p=e.dirname),Je("dirname",e.dirname),Je("relativePath",e.relativePath),Je("cwd",p);let d=c.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:d.allowTriggerPanic,prismaPath:d.binaryPath??void 0,engineEndpoint:d.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&ru(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(m=>typeof m=="string"?m==="query":m.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:rl(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Mt,getBatchRequestPayload:At,prismaGraphQLToJSError:Dn,PrismaClientUnknownRequestError:Y,PrismaClientInitializationError:_,PrismaClientKnownRequestError:B,debug:q("prisma:client:accelerateEngine"),engineVersion:yu.version,clientVersion:e.clientVersion}},Je("clientVersion",e.clientVersion),this._engine=Bc(e,this._engineConfig),this._requestHandler=new si(this,i),l.log)for(let m of l.log){let y=typeof m=="string"?m:m.emit==="stdout"?m.level:null;y&&this.$on(y,g=>{Zt.log(`${Zt.tags[y]??""}`,g.message||g.query)})}}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=hr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{as()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:qo({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=gu(n,i);return Vo(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new ee("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Vo(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new ee(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Qc,callsite:Be(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:qo({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...gu(n,i));throw new ee("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new ee("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=gy.nextId(),s=tu(n.length),a=n.map((l,c)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let u=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:c,isolationLevel:u,lock:s};return l.requestTransaction?.(p)??l});return fu(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let c={kind:"itx",...a};l=await n(this._createItxClient(c)),await this._engine.transaction("commit",o,a)}catch(c){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),c}return l}_createItxClient(n){return he(hr(he(qa(this),[te("_appliedParent",()=>this._appliedParent._createItxClient(n)),te("_createPrismaPromise",()=>Uo(n)),te(fy,()=>n.id)])),[Tt(Ha)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??my,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=async l=>{let{runInTransaction:c,args:u,...p}=l,d={...n,...p};u&&(d.args=i.middlewareArgsToRequestArgs(u)),n.transaction!==void 0&&c===!1&&delete d.transaction;let m=await Ka(this,d);return d.model?Qa({result:m,modelName:d.model,args:d.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):m};return this._tracingHelper.runInChildSpan(s.operation,()=>new uy("prisma-client-request").runInAsyncScope(()=>a(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:c,transaction:u,unpacker:p,otelParentCtx:d,customDataProxyFetch:m}){try{n=c?c(n):n;let y={name:"serialize"},g=this._tracingHelper.runInChildSpan(y,()=>Vi({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return q.enabled("prisma:client")&&(Je("Prisma Client call:"),Je(`prisma.${i}(${Ia(n)})`),Je("Generated request:"),Je(JSON.stringify(g,null,2)+`
`)),u?.kind==="batch"&&await u.lock,this._requestHandler.request({protocolQuery:g,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:u,unpacker:p,otelParentCtx:d,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:m})}catch(y){throw y.clientVersion=this._clientVersion,y}}$metrics=new mr(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=Ua}return t}function gu(e,t){return hy(e)?[new pe(e,t),Yc]:[e,Zc]}function hy(e){return Array.isArray(e)&&Array.isArray(e.raw)}var wy=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Ey(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!wy.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}function xy(e){tr(e,{conflictCheck:"warn"})}export{mn as DMMF,q as Debug,ae as Decimal,zo as Extensions,mr as MetricsClient,_ as PrismaClientInitializationError,B as PrismaClientKnownRequestError,oe as PrismaClientRustPanicError,Y as PrismaClientUnknownRequestError,ee as PrismaClientValidationError,Zo as Public,pe as Sql,Yd as createParam,am as defineDmmfProperty,Ze as deserializeJsonResponse,Ho as deserializeRawResult,yp as dmmfToRuntimeDataModel,pm as empty,yy as getPrismaClient,rf as getRuntime,um as join,Ey as makeStrictEnum,cm as makeTypedQueryFactory,_i as objectEnumValues,Aa as raw,Vi as serializeJsonQuery,Li as skip,Ca as sqltag,xy as warnEnvConflicts,rn as warnOnce};
/*! Bundled license information:

@noble/hashes/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=client.mjs.map
