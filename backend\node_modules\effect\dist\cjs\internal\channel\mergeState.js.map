{"version": 3, "file": "mergeState.js", "names": ["_Function", "require", "_Predicate", "OpCodes", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MergeStateSymbolKey", "MergeStateTypeId", "exports", "Symbol", "for", "proto", "BothRunning", "left", "right", "op", "create", "_tag", "OP_BOTH_RUNNING", "LeftDone", "OP_LEFT_DONE", "RightDone", "OP_RIGHT_DONE", "isMergeState", "u", "hasProperty", "isBothRunning", "self", "isLeftDone", "isRightDone", "match", "dual", "onBothRunning", "onLeftDone", "onRightDone"], "sources": ["../../../../src/internal/channel/mergeState.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAIA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,uBAAA,CAAAH,OAAA;AAA0D,SAAAG,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,CAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE1D;AACA,MAAMkB,mBAAmB,GAAG,0BAA0B;AAEtD;AACO,MAAMC,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,gBAAgCE,MAAM,CAACC,GAAG,CACrEJ,mBAAmB,CACW;AAEhC;AACA,MAAMK,KAAK,GAAG;EACZ,CAACJ,gBAAgB,GAAGA;CACrB;AAED;AACO,MAAMK,WAAW,GAAGA,CACzBC,IAAiD,EACjDC,KAAoD,KACqB;EACzE,MAAMC,EAAE,GAAGZ,MAAM,CAACa,MAAM,CAACL,KAAK,CAAC;EAC/BI,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAACiC,eAAe;EACjCH,EAAE,CAACF,IAAI,GAAGA,IAAI;EACdE,EAAE,CAACD,KAAK,GAAGA,KAAK;EAChB,OAAOC,EAAE;AACX,CAAC;AAED;AAAAP,OAAA,CAAAI,WAAA,GAAAA,WAAA;AACO,MAAMO,QAAQ,GACnBxB,CAAoE,IACK;EACzE,MAAMoB,EAAE,GAAGZ,MAAM,CAACa,MAAM,CAACL,KAAK,CAAC;EAC/BI,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAACmC,YAAY;EAC9BL,EAAE,CAACpB,CAAC,GAAGA,CAAC;EACR,OAAOoB,EAAE;AACX,CAAC;AAED;AAAAP,OAAA,CAAAW,QAAA,GAAAA,QAAA;AACO,MAAME,SAAS,GACpB1B,CAAkE,IACO;EACzE,MAAMoB,EAAE,GAAGZ,MAAM,CAACa,MAAM,CAACL,KAAK,CAAC;EAC/BI,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAACqC,aAAa;EAC/BP,EAAE,CAACpB,CAAC,GAAGA,CAAC;EACR,OAAOoB,EAAE;AACX,CAAC;AAED;AAAAP,OAAA,CAAAa,SAAA,GAAAA,SAAA;AACO,MAAME,YAAY,GACvBC,CAAU,IAEV,IAAAC,sBAAW,EAACD,CAAC,EAAEjB,gBAAgB,CAAC;AAElC;AAAAC,OAAA,CAAAe,YAAA,GAAAA,YAAA;AACO,MAAMG,aAAa,GACxBC,IAA2E,IACO;EAClF,OAAOA,IAAI,CAACV,IAAI,KAAKhC,OAAO,CAACiC,eAAe;AAC9C,CAAC;AAED;AAAAV,OAAA,CAAAkB,aAAA,GAAAA,aAAA;AACO,MAAME,UAAU,GACrBD,IAA2E,IACI;EAC/E,OAAOA,IAAI,CAACV,IAAI,KAAKhC,OAAO,CAACmC,YAAY;AAC3C,CAAC;AAED;AAAAZ,OAAA,CAAAoB,UAAA,GAAAA,UAAA;AACO,MAAMC,WAAW,GACtBF,IAA2E,IACK;EAChF,OAAOA,IAAI,CAACV,IAAI,KAAKhC,OAAO,CAACqC,aAAa;AAC5C,CAAC;AAED;AAAAd,OAAA,CAAAqB,WAAA,GAAAA,WAAA;AACO,MAAMC,KAAK,GAAAtB,OAAA,CAAAsB,KAAA,gBAAG,IAAAC,cAAI,EAsBvB,CAAC,EAAE,CACHJ,IAAI,EACJ;EAAEK,aAAa;EAAEC,UAAU;EAAEC;AAAW,CAAE,KACxC;EACF,QAAQP,IAAI,CAACV,IAAI;IACf,KAAKhC,OAAO,CAACiC,eAAe;MAAE;QAC5B,OAAOc,aAAa,CAACL,IAAI,CAACd,IAAI,EAAEc,IAAI,CAACb,KAAK,CAAC;MAC7C;IACA,KAAK7B,OAAO,CAACmC,YAAY;MAAE;QACzB,OAAOa,UAAU,CAACN,IAAI,CAAChC,CAAC,CAAC;MAC3B;IACA,KAAKV,OAAO,CAACqC,aAAa;MAAE;QAC1B,OAAOY,WAAW,CAACP,IAAI,CAAChC,CAAC,CAAC;MAC5B;EACF;AACF,CAAC,CAAC", "ignoreList": []}