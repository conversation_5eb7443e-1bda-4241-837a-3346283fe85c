{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.15.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "prisma": "^6.15.0"}, "devDependencies": {"nodemon": "^3.1.10"}}